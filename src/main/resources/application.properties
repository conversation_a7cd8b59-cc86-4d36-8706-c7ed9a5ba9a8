spring.datasource.url=jdbc:postgresql://${POSTGRES_HOST_URI:localhost:5432}/${POSTGRES_DB:evahana}?currentSchema=evdata,evusers,public
spring.read.datasource.url=jdbc:postgresql://${POSTGRES_REPLICA_HOST_URI:localhost:5432}/${POSTGRES_DB:evahana}?currentSchema=evdata,evusers,public
spring.jpa.database=POSTGRESQL
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.username=${POSTGRES_USER:esarathi}
# Flyway Configuration
spring.flyway.baseline-version=1.0.6
spring.flyway.baselineOnMigrate=true
spring.datasource.password=${POSTGRES_PASSWORD:ifPwd}
spring.jpa.hibernate.ddl-auto=${DB_DDL:none}
spring.jpa.generate-ddl=false
spring.jpa.show-sql=false
spring.flyway.schemas=evdata,evusers,public

#spring.jpa.hibernate.ddl-auto=none
# Application properties
#redis config
spring.redis.host=${REDIS_HOST:localhost}
spring.redis.port=${REDIS_PORT:6379}
spring.redis.password=${REDIS_PASSWORD:guest}
spring.redis.username=${REDIS_USERNAME:default}
spring.cache.redis.time-to-live=${REDIS_TTL:0}
logging.level.org.springframework.data.redis=${REDIS_LOG_LEVEL:ERROR}
spring.cache.redis.ttl-grv-agg-hours=24
spring.cache.redis.grv-agg-prefix=IMEI_GRV_AGG::
spring.cache.redis.grv-raw-agg-prefix=IMEI_GRV_RAW_AGG::
spring.cache.redis.grv-null-placeholder=NULL
jwt.secret=d997ebbb-ca49-4a3b-944d-936fced41052
jwt.issuer=nds
# General app settings
#
#
aws.profile=${AWS_PROFILE}
#
# Properties for Java mail server
# under the namespace spring.mail
#
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=${MAIL_USER}
spring.mail.password=${MAIL_PASSWORD}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
#
#
app.enableOAuth2Registration=true
#
#
spring.boot.admin.client.url=http://localhost:8080
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always
#
#
# swagger
#
#
spring.mvc.pathmatch.matching-strategy=ant-path-matcher
application.base-url=${APPLICATION_BASE_URL:http://localhost:8080}
#
#
#
#
#spring.sql.init.mode=always
server.forward-headers-strategy=framework
spring.profiles.active=nds
# Parent organisation identifier.
#
usermgmt.organisations.default-organisation-identifier=parent
usermgmt.organisation.registration.send-notification=false
#logging.level.root=debug
#spring.jpa.show-sql=true
spring.datasource.test-while-idle=true
spring.datasource.validation-query=SELECT 1;
#spring.datasource.hikari.idleTimeout=600000
#spring.datasource.hikari.maxLifetime=1800000
#spring.datasource.hikari.minimum-idle=10
#spring.datasource.hikari.maximum-pool-size=100
#spring.datasource.hikari.connection-timeout=90000
server.servlet.context-path=/ev
spring.main.allow-bean-definition-overriding=true
#logging.level.root=debug
## for RabbitMq
#spring.rabbitmq.host=localhost
spring.rabbitmq.host=${RABBIT_MQ_HOST:localhost}
spring.rabbitmq.port=5672
spring.rabbitmq.virtual-host=${VIRTUAL_HOST:evahana}
spring.rabbitmq.username=${RABBIT_MQ_USERNAME:guest}
spring.rabbitmq.password=${RABBIT_MQ_PASSWORD:guest}
rabbitmq.queue.trip-details-input=${TRIP_DETAILS_INPUT_QUEUE:TRIP_DETAILS_INPUT_QUEUE}
rabbitmq.queue.trip-details-output=${TRIP_DETAILS_OUTPUT_QUEUE:TRIP_DETAILS_OUTPUT_QUEUE}
rabbitmq.queue.telemetry.metrics.save=${TELEMETRY_PERSISTENCE_QUEUE:TELEMETRY_SAVE}
rabbitmq.queue.telemetry.location.save=${LOCATION_PERSISTENCE_QUEUE:LOCATION_PERSISTENCE_QUEUE}
rabbitmq.queue.telemetry.battery.cell.save=${BATTERY_CELL_PERSISTENCE_QUEUE:BATTERY_CELL_PERSISTENCE_QUEUE}
rabbitmq.queue.telemetry.battery.stack.save=${BATTERY_STACK_PERSISTENCE_QUEUE:BATTERY_STACK_PERSISTENCE_QUEUE}
rabbitmq.queue.telemetry.battery.status.save=${BATTERY_STATUS_PERSISTENCE_QUEUE:BATTERY_STATUS_PERSISTENCE_QUEUE}
rabbitmq.queue.csv.request.save=${CSV_REQUEST_QUEUE:CSV_REQUEST_QUEUE}
rabbitmq.queue.csv.response.save=${CSV_RESPONSE_QUEUE:CSV_RESPONSE_QUEUE}
rabbitmq.queue.running-event-queue=${RUNNING_VEHICLE_EVENT_QUEUE:RUNNING_VEHICLE_EVENT_QUEUE}
rabbitmq.queue.part-health-monitor-queue=${PART_HEALTH_MONITOR_QUEUE:PART_HEALTH_MONITOR_QUEUE}
rabbitmq.queues=CSV_REQUEST_QUEUE,CSV_RESPONSE_QUEUE,RUNNING_VEHICLE_EVENT_QUEUE,PART_HEALTH_MONITOR_QUEUE
aws.accessKey=${AWS_ACCESS_KEY}
aws.secretKey=${AWS_SECRET_KEY}
aws.region=${AWS_REGION:ap-south-1}
s3.profile.image.bucket.name=${IMAGE_BUCKET:com.nichesolv.dev1.profile.image}
s3.profile.bucket.url=${S3_PROFILE_PIC_BUCKET_URL:https://s3.ap-south-1.amazonaws.com}
s3.csv.reports.bucket.name=${S3_CSV_REPORTS_BUCKET_NAME:ev-csv-reports-shared}
#S3 Base URL
organisation.s3.base.url=${S3_BASE_URL:https://s3.ap-south-1.amazonaws.com/com.nichesolv.resource.dev/}
#Reverse Geocoding
location.service.endpoint=${REVERSE_GEOCODING_API:https://location.ev-be-dev.nichesolv.com/reversegeocoding}
grafana_base_url=${GRAFANA_BASE_URL:http://localhost:3000}
grafana_database_url=${POSTGRES_HOST_URI:http://localhost:5432}
grafana_database=${GRAFANA_POSTGRES_DB:evahana}
grafana_user=${GRAFANA_POSTGRES_USER:esarathi}
grafana_password=${GRAFANA_POSTGRES_PASSWORD:ifPwd}
grafana.datasource.uid=${GRAFANA_DATASOURCE_UID:evahana_uid}
grafana.datasource.name=${GRAFANA_DATASOURCE_NAME:EVahana}
grafana.datasource.db.logoUrl=${GRAFANA_DATASOURCE_DB_TYPE_LOGO:public/app/plugins/datasource/postgres/img/postgresql_logo.svg}
grafana.datasource.db.name=${GRAFANA_DATASOURCE_DATABASE:evahana}
grafana.datasource.db.type=${GRAFANA_DATASOURCE_DB_TYPE:postgres}
grafana.datasource.type.name=${GRAFANA_DATASOURCE_DB_TYPE_NAME:PostgreSQL}
v2.analytics.endpoint=${TEST_RIDE_ANALYTICS_ENDPOINT:http://localhost:8081/trip_details}
tyre.pressure.endpoint=${TYRE_PRESSURE_ENDPOINT:http://localhost:8081/tyre_pressure}
spring.jackson.deserialization.FAIL_ON_UNKNOWN_PROPERTIES=false
apple.test.phonenumber=${APPLE_TEST_PHONE_NUMBER}
triphistory.cron.expression=0 */30 * * * ?
rabbitmq.queue.trip-history-request=TRIP_HISTORY_REQUEST
rabbitmq.queue.trip-history-response=TRIP_HISTORY_RESPONSE

grafana_javaAdmin_username=${GRAFANA_JAVA_ADMIN_USER_NAME:javaadmin}
grafana_javaAdmin_password=${GRAFANA_JAVA_ADMIN_PASSWORD:jav@Admin}
mainOrganisationUrlSlug=${MAIN_ORGANISATION_URL_SLUG:nichesolv}
adminResetMailId=${ADMIN_PASSWORD_RESET_MAIL_ID:<EMAIL>}

#to create flyway baseline version DDL
#spring.flyway.enabled=false
#spring.jpa.properties.javax.persistence.schema-generation.create-source=metadata
#spring.jpa.properties.javax.persistence.schema-generation.scripts.action=create
#spring.jpa.properties.javax.persistence.schema-generation.scripts.create-target=src/main/resources/V0.0.0__schemaBaseline.sql
#CSV Report table names
table.headers.telemetry=imei,timestamp,accel_x_axis,accel_y_axis,accel_z_axis,ai_lean_angle,ai_system_voltage,ai_temperature,ai_vbuck,ai_voltage_input,ai_vusr1,ai_vusr2,co_relation_id,created_on,di_ignition,di_main_power,di_motion,di_tamper,di_usr1,di_usr2,do_usr2,di_usr2,gyro_x_axis,gyro_y_axis,gyro_z_axis,motor_brake,motor_cruise,motor_dc_current,motor_dc_voltage,motor_driving_mode,motor_fault_feedback,motor_mcs_temperature,motor_parking_sign,motor_ready_sign,motor_regeneration,motor_reverse,motor_side_stand,motor_speed,motor_temperature,motor_throttle,packet_received_on,mfr_org_id,owner_org,vehicle_id,vehicle_model_id,owner_org_id
table.headers.battery=imei,timestamp,battery_volt,cell_volt_max,cell_volt_min,chg_cycle_count,co_relation_id,created_on,current,dsg_cycle_count,packet_received_on,soc,soh,temperature_max,temperature_min,mfr_org_id,owner_org,vehicle_id,owner_org_id

#firebase.serviceAccountKeyPath=${FIREBASE_ADMIN_FILE_PATH}
firebase.serviceAccountKeyPath=${FIREBASE_ADMIN_FILE_PATH}
s3.firebase.admin.file.name=evapp-firebase-adminsdk.json
s3.firebase.admin.file.bucket.name=ev-firebase-config
#Refresh materialized views
refresh-materialized-views.cron.expression=0 0 2 15 * ?
daily-refresh-materialized-views.cron.expression=0 0 1 * * ?
refresh-materialized-views.cron.timezone=IST
refresh-materialized-views.view-names=view_drive_mode_range_and_speed,view_vehicle_model_part_model_attribute,view_vehicle_part_part_model
daily-refresh-materialized-views.view-names=view_vehicle_daily_usage
spring.flyway.enabled=${FLYWAY_ENABLED:true}
application.base.url=${APPLICATION_BASE_URL:http://localhost:8080}
nds.msg91.authkey=${NDS_MSG91_AUTH_KEY:422108AojEC6yWKQz665d58b4P1}
nichesolv.msg91.authkey=${NICHESOLV_MSG91_AUTH_KEY:422108AojEC6yWKQz665d58b4P1}
lml.msg91.authkey=${LML_MSG91_AUTH_KEY:422108AojEC6yWKQz665d58b4P1}
simpson.msg91.authkey=${SIMPSON_MSG91_AUTH_KEY:422108AojEC6yWKQz665d58b4P1}
administrator.org=${ADMINISTRATOR_ORG:NicheSolv}
logging.level.com.nichesolv.evahanam=${LOG_LEVEL:INFO}
webApp.jwt.expiry=${WEBAPP_JWT_EXPIRY:2592000000}
mobApp.jwt.expiry=${MOBAPP_JWT_EXPIRY:5184000000}
data.delay.interval=30sec
