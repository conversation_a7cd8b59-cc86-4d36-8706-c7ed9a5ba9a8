package com.nichesolv.evahanam.trip.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.nichesolv.evahanam.common.jpa.DataFrequencyPlanDetails;
import com.nichesolv.evahanam.evApp.jpa.UserVehicleConnection;
import com.nichesolv.evahanam.trip.dto.tripsummary.TripsToArchiveAndRecalculatedTimeWindows;
import com.nichesolv.evahanam.trip.dto.tripsummary.VehicleStatusTimeWindowProjection;
import com.nichesolv.evahanam.trip.enums.TripType;
import com.nichesolv.evahanam.vehicle.dto.VehicleModelDto;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.trip.dto.TestRideV2;
import com.nichesolv.evahanam.trip.dto.TripDto;
import com.nichesolv.evahanam.trip.dto.TripListAllDto;
import com.nichesolv.evahanam.trip.dto.cumulative.CumulativeResponse;
import com.nichesolv.evahanam.trip.dto.cumulative.input.CumulativeInputDto;
import com.nichesolv.evahanam.trip.dto.cumulative.input.FilterDto;
import com.nichesolv.evahanam.trip.dto.cumulative.input.FilterInputDto;
import com.nichesolv.evahanam.trip.dto.cumulative.response.FilterResponseDto;
import com.nichesolv.evahanam.trip.dto.cumulative.response.TripCountByFilterResponse;
import com.nichesolv.evahanam.trip.dto.tripsummary.TripProjection;
import com.nichesolv.evahanam.trip.dto.tripsummary.TripSummaryDto;
import com.nichesolv.evahanam.trip.dto.v2.RangeDto;
import com.nichesolv.evahanam.trip.dto.v2.V2TestRideInput;
import com.nichesolv.evahanam.trip.enums.TestRideSummaryPopulationStatus;
import com.nichesolv.evahanam.trip.enums.UserVehicleTripEvents;
import com.nichesolv.evahanam.trip.jpa.Trip;
import com.nichesolv.evahanam.trip.rabbitmq.dto.TripHistoryResponse;
import com.nichesolv.evahanam.trip.rabbitmq.dto.TripResponseModel;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.usermgmt.user.model.organisation.Organisation;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

//@Repository
public interface TripSummaryService {
    TripSummaryDto getTripSummary(Long tripId, CustomOrganisation organisation);

    Page<TripProjection> getTripList(String imei, Pageable page);

    void processAutomatedTrips(@NotNull TripHistoryResponse tripHistoryResponse);

    Trip saveTrip(TripDto tripDto);

    public void saveAutomaticTrips(Vehicle vehicle, Long runningTime, Long stoppageTime , TestRideSummaryPopulationStatus status , Instant currentTime, List<Trip> inProgressTrip);

    public void saveDataDelayedAutomaticTrips(String imei, Instant currentTime, DataFrequencyPlanDetails dataFrequencyPlanDetails);

    public TripsToArchiveAndRecalculatedTimeWindows getTripsTobeArchivedAndNewTimeWindows(String imei, List<VehicleStatusTimeWindowProjection> dataDelayTimeWindows, Integer thresholdInterval);

    public List<Trip> saveDataDelayedVehicleTrips(String imei, Instant startTime, Instant endTime, DataFrequencyPlanDetails dataFrequencyPlanDetails);

    public void saveDataDelayedUserTrips(List<Trip> vehicleTrips);

    void saveUserVehicleTrip(Trip trip, UserVehicleConnection connection , UserVehicleTripEvents event);

    CumulativeResponse getCumulativeData(CumulativeInputDto cumulativeInputDto, Long organisationId) throws JsonProcessingException;

    FilterResponseDto getFilterData(FilterDto filterDto, Long organisationId);

    void saveGeneratedTripStats(TestRideV2 tripResponseModel) throws JsonProcessingException;

    //void saveGeneratedTripStats(TripResponseModel tripResponseModel, RangeDto rangeDto, Long tripId) throws JsonProcessingException;

    TripCountByFilterResponse getTripCountByFilter(FilterInputDto filterInputDto);

    TestRideV2 getV2AnalyticsData(V2TestRideInput v2TestRideInput);

    void processTripDetails(Trip trip);

    List<Trip> findByImeiAndStatusAndTripType(Vehicle vehicle, TestRideSummaryPopulationStatus status, TripType tripType);

    TripListAllDto getVehicleTripsBetween(String imei, TripType tripType, Long from, Long to, Pageable pageable);
}
