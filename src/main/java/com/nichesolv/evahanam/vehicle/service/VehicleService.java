package com.nichesolv.evahanam.vehicle.service;

import com.nichesolv.evahanam.charging.repository.ChargingEventDetailsRepository;
import com.nichesolv.evahanam.common.enums.EntityType;
import com.nichesolv.evahanam.common.events.VehicleEvent;
import com.nichesolv.evahanam.common.events.VehicleStateEvent;
import com.nichesolv.evahanam.common.jpa.*;

import com.nichesolv.evahanam.common.jpa.AggregateName;
import com.nichesolv.evahanam.common.jpa.DataFrequencyPlan;
import com.nichesolv.evahanam.common.jpa.DataFrequencyPlanDetails;
import com.nichesolv.evahanam.cache.service.VehicleCacheRetrievalService;
import com.nichesolv.evahanam.cache.service.VehicleCacheRefreshService;
import com.nichesolv.evahanam.common.exception.ImageException;
import com.nichesolv.evahanam.common.repository.*;
import com.nichesolv.evahanam.common.util.EvMessageBundle;
import com.nichesolv.evahanam.evApp.exception.OrganisationNotFoundException;
import com.nichesolv.evahanam.evApp.exception.UserAlreadyPairedException;
import com.nichesolv.evahanam.evApp.exception.UserProfileNotFoundException;
import com.nichesolv.evahanam.evApp.jpa.UserVehicleConnection;
import com.nichesolv.evahanam.evApp.repository.UserVehicleConnectionRepository;
import com.nichesolv.evahanam.evApp.service.IVehicleLocationService;
import com.nichesolv.evahanam.telemetryData.dto.*;
import com.nichesolv.evahanam.telemetryData.jpa.TelemetryIdx;
import com.nichesolv.evahanam.telemetryData.jpa.VehicleBatteryData;
import com.nichesolv.evahanam.telemetryData.jpa.VehicleLocationData;
import com.nichesolv.evahanam.telemetryData.repository.LocationDataRepository;
import com.nichesolv.evahanam.telemetryData.repository.MotorDataRepository;
import com.nichesolv.evahanam.telemetryData.repository.TelemetryBatteryRepository;
import com.nichesolv.evahanam.telemetryData.repository.VehicleDataRepository;
import com.nichesolv.evahanam.telemetryData.repository.bmsrepo.BatteryAlarmRepository;
import com.nichesolv.evahanam.telemetryData.service.batteryData.AlertService;
import com.nichesolv.evahanam.telemetryData.util.CommonQueryUtility;
import com.nichesolv.evahanam.trip.enums.TripDetailsFields;
import com.nichesolv.evahanam.util.HttpRequestOriginUtil;
import com.nichesolv.evahanam.util.UserOrganisationUtils;
import com.nichesolv.evahanam.vehicle.dto.*;
import com.nichesolv.evahanam.vehicle.dto.metadata.VehicleMetaData;
import com.nichesolv.evahanam.vehicle.dto.metadata.VehicleMinMaxMotorData;
import com.nichesolv.evahanam.vehicle.dto.metadata.VehicleTestMetaData;
import com.nichesolv.evahanam.vehicle.enums.OperationStatus;
import com.nichesolv.evahanam.vehicle.enums.UpdateSource;
import com.nichesolv.evahanam.vehicle.enums.VehicleIdentifierTypes;
import com.nichesolv.evahanam.vehicle.enums.VehicleState;
import com.nichesolv.evahanam.vehicle.events.VehicleCreatedEvent;
import com.nichesolv.evahanam.vehicle.enums.TimeFilter;
import com.nichesolv.evahanam.vehicle.exception.*;
import com.nichesolv.evahanam.vehicle.jpa.*;
import com.nichesolv.evahanam.vehicle.repository.*;
import com.nichesolv.evahanam.vehicleModel.dto.DriveModeSpeedDto;
import com.nichesolv.evahanam.vehicleModel.dto.PartModelAttributeDto;
import com.nichesolv.evahanam.vehicleModel.dto.PartModelTreeDto;
import com.nichesolv.evahanam.vehicleModel.enums.PartType;
import com.nichesolv.evahanam.vehicleModel.exception.PartModelException;
import com.nichesolv.evahanam.vehicleModel.exception.VehicleModelException;
import com.nichesolv.evahanam.vehicleModel.jpa.*;
import com.nichesolv.evahanam.vehicleModel.repository.ColorModelRepository;
import com.nichesolv.evahanam.vehicleModel.repository.PartModelRepository;
import com.nichesolv.evahanam.vehicleModel.repository.VehicleModelRepository;
import com.nichesolv.evahanam.vehicleModel.repository.*;
import com.nichesolv.evahanam.vehicleModel.service.ColorModelService;
import com.nichesolv.evahanam.vehicleTests.enums.TestStatus;
import com.nichesolv.evahanam.vehicleTests.enums.TestTypeName;
import com.nichesolv.evahanam.vehicleTests.repository.TestTypeRepository;
import com.nichesolv.evahanam.vehicleTests.repository.VehicleTestRepository;
import com.nichesolv.evahanam.trip.enums.TestRideSummaryPopulationStatus;
import com.nichesolv.evahanam.trip.enums.TripType;
import com.nichesolv.evahanam.trip.jpa.VehicleEventMonitor;

import com.nichesolv.evahanam.trip.repository.TripRepository;
import com.nichesolv.evahanam.trip.repository.VehicleEventMonitorRepository;

import com.nichesolv.nds.controller.organisation.OrganisationControllerImpl;
import com.nichesolv.nds.dto.organisation.CustomOrganisationDto;
import com.nichesolv.nds.dto.organisation.enums.OrganisationType;
import com.nichesolv.nds.dto.user.registration.B2cUserRegistrationDto;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.model.user.CustomUser;
import com.nichesolv.nds.repository.CustomOrganisationRepository;
import com.nichesolv.nds.repository.CustomUserRepository;
import com.nichesolv.nds.service.user.UserServiceImpl;
import com.nichesolv.usermgmt.user.dto.organisation.AddressDto;
import com.nichesolv.usermgmt.user.exception.UserNotFoundException;
import com.nichesolv.usermgmt.user.model.organisation.OrganisationImpl;
import com.nichesolv.usermgmt.user.model.user.BaseUser;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.*;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Slf4j
@Service
public class VehicleService implements IVehicleService {

    @Autowired
    PartRepository partRepository;

    @Autowired
    BatteryAlarmRepository batteryAlarmRepository;

    @Autowired
    HttpRequestOriginUtil httpRequestOriginUtil;

    @Autowired
    HttpServletRequest httpServletRequest;

    @Autowired
    VehicleRepository vehicleRepository;

    @Autowired
    MotorDataRepository motorDataRepository;

    @Autowired
    VehicleTestRepository vehicleTestRepository;
    @Autowired
    VehicleRegistrationDetailsRepository vehicleRegistrationDetailsRepository;
    @Autowired
    VehicleDataRepository vehicleDataRepository;

    @Autowired
    TelemetryBatteryRepository telemetryBatteryRepository;

    @Autowired
    CustomOrganisationRepository customOrganisationRepository;

    @Autowired
    ColorModelRepository colorModelRepository;

    @Autowired
    VehicleModelRepository vehicleModelRepository;

    @Autowired
    ColorModelService colorModelService;

    @Autowired
    ImageRepository imageRepository;

    @Autowired
    VehicleModelPartModelImageRepository vehicleModelPartModelImageRepository;

    @Autowired
    UserServiceImpl userService;

    @Autowired
    OrganisationControllerImpl organisationController;

    @Autowired
    LocationDataRepository locationDataRepository;

    @Autowired
    IVehicleLocationService iVehicleLocationService;

    @Autowired
    VehicleRunningMetricsRepository vehicleRunningMetricsRepository;

    @Autowired
    VehicleSaleLogRepository vehicleSaleLogRepository;

    @Autowired
    VehicleStatusRepository vehicleStatusRepository;

    @Autowired
    AlertService alertService;

    @Autowired
    PartModelRepository partModelRepository;

    @Autowired
    PartReplacementLogRepository partReplacementLogRepository;

    @Autowired
    CustomUserRepository userRepository;

    @Autowired
    FleetRepository fleetRepository;

    @Autowired
    CommonQueryUtility commonQueryUtility;

    @Autowired
    RangeAlertRepository rangeAlertRepository;

    @Autowired
    EntityManager entityManager;

    @Autowired
    PartService partService;

    @Autowired
    VehicleLatestDataRepository vehicleLatestDataRepository;

    @Autowired
    VehicleModelSubscriptionRepository vehicleModelSubscriptionRepository;

    @Autowired
    OrganisationSubscriptionRepository organisationSubscriptionRepository;


    String dcVoltageMax = "dcVoltageMax";
    String dcVoltageMin = "dcVoltageMin";
    String dcCurrentMax = "dcCurrentMax";
    String dcCurrentMin = "dcCurrentMin";
    String mcsTempMax = "mcsTempMax";
    String mcsTempMin = "mcsTempMin";
    String motorTempMax = "motorTempMax";
    String motorTempMin = "motorTempMin";
    String batterySensorTempMax = "batterySensorTempMax";
    String batterySensorTempMin = "batterySensorTempMin";

    String fullCapacityAttributeName = "fullCapacity";

    List<Integer> filterSensorIds = Arrays.asList(5, 6);

    @Autowired
    EvMessageBundle evMessageBundle;

    @Autowired
    VehicleEventMonitorRepository vehicleEventMonitorRepository;

    @Autowired
    TripRepository tripRepository;


    Long secondsInWeek = 7 * 24 * 60 * 60L;

    @Value("${data.delay.interval}")
    String dataDelayInterval;

    @Autowired
    TestTypeRepository testTypeRepository;

    @Autowired
    VehicleCacheRetrievalService vehicleCacheRetrievalService;

    @Autowired
    VehicleCacheRefreshService vehicleCacheRefreshService;
    @Autowired
    UserOrganisationUtils userOrganisationUtils;

    @Autowired
    ActiveVehicleSubscriptionPlanRepository activeVehicleSubscriptionPlanRepository;

    @Autowired
    VehicleTestDurationRepository vehicleTestDurationRepository;
    @Autowired
    UserVehicleConnectionRepository userVehicleConnectionRepository;

    private final ApplicationEventPublisher eventPublisher;


    public VehicleService(VehicleRepository vehicleRepository, ApplicationEventPublisher eventPublisher) {
        this.vehicleRepository = vehicleRepository;
        this.eventPublisher = eventPublisher;
    }


    @Override
    @Transactional
    public Long createVehicle(ErpVehicleDto newVehicleDto, BaseUser user) {
        log.info(newVehicleDto.toString());

        CustomOrganisation owner = Optional.ofNullable(customOrganisationRepository.findByName(
                newVehicleDto.getOwnerName())).orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("ORG_NOT_FOUND_WITH_ORG_INFO", newVehicleDto.getOwnerName())));

        CustomOrganisation manufacturer = Optional.ofNullable(customOrganisationRepository.findByName(
                newVehicleDto.getManufacturerName())).orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("ORG_NOT_FOUND_WITH_ORG_INFO") + newVehicleDto.getManufacturerName()));

        VehicleModel vehicleModel = vehicleModelRepository.findByNameIgnoreCaseAndManufacturer(newVehicleDto.getVehicleModelName(), manufacturer).orElseThrow(() -> new VehicleModelException(evMessageBundle.getMessage("VEHICLE_MODEL_NOT_FOUND", newVehicleDto.getVehicleModelName())));
        ColorModel colorModel = colorModelRepository.findByNameIgnoreCaseAndManufacturer(newVehicleDto.getColor(), manufacturer).orElseThrow(() -> new PartModelException(evMessageBundle.getMessage("COLOR_MODEL_NOT_FOUND", newVehicleDto.getColor())));
        Optional<Vehicle> existingVehicle = getVehicle(newVehicleDto.getChassisNumber());
        if (existingVehicle.isPresent()) {
            throw new ChassisNumberException(evMessageBundle.getMessage("CHASSIS_NUMBER_ALREADY_EXISTS", newVehicleDto.getChassisNumber()));
        }
        Optional<Vehicle> existingVehicle2 = getVehicle(newVehicleDto.getImei());
        if (existingVehicle2.isPresent()) {
            throw new VehicleException(evMessageBundle.getMessage("VEHICLE_ALREADY_EXISTS", newVehicleDto.getImei()));
        }
        Vehicle vehicle = new Vehicle();
        vehicle.setImei(newVehicleDto.getImei());
        vehicle.setVehicleModel(vehicleModel);
        vehicle.setColorModel(colorModel);
        vehicle.setMfrDate(newVehicleDto.getManufacturedDate());
        vehicle.setOwner(owner);
        vehicle.setOperationStatus(OperationStatus.ACTIVE);
        vehicle.setManufacturer(manufacturer);
        vehicle.setChassisNumber(newVehicleDto.getChassisNumber());
        vehicle.setEncryptionKey(newVehicleDto.getEncryptionKey());
        vehicle.setDeviceAdvertisingName(newVehicleDto.getDeviceAdvertisingName());
        Vehicle savedVehicle = vehicleRepository.save(vehicle);
        eventPublisher.publishEvent(new VehicleCreatedEvent(this, savedVehicle));
        log.info("vehicle saved {} ", vehicle.getImei());
        //GSM part type with IMEI as serial number is added
        PartDto partDto = new PartDto();
        partDto.setPartModelName("ajjas-1");
        partDto.setSerialNumber(newVehicleDto.getImei());
        partDto.setPartType(PartType.GSM);
        partDto.setManufacturerName("ajjas");
        partDto.setManufacturedDate(Instant.now());
        if (newVehicleDto.getParts() == null) {
            newVehicleDto.setParts(new ArrayList<>());
        }
        newVehicleDto.getParts().add(partDto);
        newVehicleDto.getParts().forEach(part -> {
            partService.addPart(part);
            AddPartRequestDto addPartRequestDto = new AddPartRequestDto(vehicle.getImei(), part.getSerialNumber(), part.getBatchNumber(), part.getPartType(), part.getManufacturerName());
            addPartToVehicle(addPartRequestDto, user);
        });
        vehicleLatestDataRepository.save(new VehicleLatestData(vehicle, vehicle.getImei()));
        return savedVehicle.getId();
    }



    public Optional<VehicleIdsProjection> populateVehicleIdentifiers(String vehImei) {
        // Fetch vehicle identifiers by imei
        return vehicleRepository.getVehicleIdentifiers(vehImei);
    }


    @Override
    public List<VehicleIdsProjection> listVehiclesByIdentifier(CustomOrganisation customOrganisation, Pageable pageable,VehicleIdentifierTypes idType,String pattern)
            throws VehicleException {

        Page<VehicleIdsProjection> ids=Page.empty();
        try {
            log.debug("idType {},pattern {}",idType,pattern);
            if(Optional.ofNullable(idType).isPresent()){

                ids= vehicleRepository.getVehiclesOfPatternByOrg(customOrganisation.getId(),pattern,idType.getLabel(),pageable);
            }else {
                ids = vehicleRepository.getVehicleIdentifiersByOrg(customOrganisation.getId(), pageable);
            }
        } catch (VehicleException e) {
            log.error("Error finding vehicle list ",e);
            throw new VehicleException(e.getMessage());
        }
        return ids.stream().toList();
    }

    private Page<Vehicle> getAllVehiclesInOrganisation(CustomOrganisation customOrganisation, Pageable pageable) {
        Page<Vehicle> vehicles;
        if (customOrganisation.getOrganisationType().equals(OrganisationType.ADMINISTRATOR)) {
            vehicles = vehicleRepository.findAll(pageable);
        } else {
            vehicles = vehicleRepository.findAllByManufacturer(customOrganisation, pageable);
        }
        return vehicles;
    }

    @Override
    public List<String> listVehicles(CustomOrganisation customOrganisation, Pageable pageable)
            throws VehicleException, VehicleModelException {
        Page<Vehicle> vehicles = getAllVehiclesInOrganisation(customOrganisation, pageable);
        return vehicles.stream().map(Vehicle::getImei)
                .collect(Collectors.toList());
    }


    @Override
    public List<String> listVehiclesWithOutPagination(CustomOrganisation customOrganisation) {
        List<Vehicle> vehicles;
        try {
            userOrganisationUtils.isFleetManager();
            String sub = SecurityContextHolder.getContext().getAuthentication().getName();
            CustomUser user = userRepository.findByEmailIgnoreCase(sub).orElseThrow(() -> new UserProfileNotFoundException("User does not exists"));
            List<Fleet> userFleets = fleetRepository.findByUsers(user.getId()).stream().filter(fleet -> fleet.getOrganisation().getId().equals(customOrganisation.getId())).toList(); // we will get only one fleet
            vehicles = userFleets.get(0).getVehicle().stream().toList();
        } catch (Exception e) {
            log.info("user is not fleet manager");
            if (customOrganisation.getOrganisationType().equals(OrganisationType.ADMINISTRATOR)) {
                vehicles = vehicleRepository.findAll();
            } else {
                vehicles = vehicleRepository.findAllByManufacturerAndOperationStatus(customOrganisation, OperationStatus.ACTIVE);
            }
        }
        return vehicles.stream().map(Vehicle::getImei)
                .collect(Collectors.toList());
    }

    @Transactional
    @Override
    public void updateVehicleStatus(OperationStatusDto operationStatusDto) {
        existsByImei(operationStatusDto.getImei());
        Vehicle vehicle = getVehicleByAnyId(operationStatusDto.getImei());

        if (operationStatusDto.getOperationStatus() != null && !operationStatusDto.getOperationStatus()
                .toString().isEmpty()) {
            vehicle.setOperationStatus(operationStatusDto.getOperationStatus());
        }
    }

    @Transactional
    @Override
    public void addPartToVehicle(AddPartRequestDto addPartRequestDto, BaseUser user) {
        existsByImei(addPartRequestDto.getImei());
        Vehicle vehicle = getVehicleByAnyId(addPartRequestDto.getImei());
        CustomOrganisation manufacturer = Optional.ofNullable(customOrganisationRepository.findByName(addPartRequestDto.getManufacturerName()))
                .orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("PART_MFR_NOT_FOUND")));
        Optional<Part> part;

        String serialNumber = addPartRequestDto.getPartSerialNumber();
        String batchNumber = addPartRequestDto.getPartBatchNumber();
        PartType partType = addPartRequestDto.getPartType();
        Optional<String> serialNumberOptional = Optional.ofNullable(serialNumber).filter(s -> !s.isEmpty());
        Optional<String> batchNumberOptional = Optional.ofNullable(batchNumber).filter(b -> !b.isEmpty());

        //Add part with both serialNo & BatchNo to the vehicle
        if (serialNumberOptional.isPresent() && batchNumberOptional.isPresent()) {
            part = partRepository.findBySerialNumberAndBatchNumberAndManufacturerAndPartType(serialNumber, batchNumber, manufacturer, partType);
            if (part.isEmpty()) {
                throw new PartException(evMessageBundle.getMessage("PART_SERIAL_BATCH_NOT_FOUND", serialNumber, batchNumber));
            }
        }
        //Add part with only BatchNo to Vehicle
        else if (batchNumberOptional.isPresent()) {
            part = partRepository.findByBatchNumberAndManufacturerAndPartType(batchNumber, manufacturer, partType);
            if (part.isEmpty()) {
                throw new PartException(evMessageBundle.getMessage("PART_BATCH_NOT_FOUND", batchNumber));
            }
        }
        //Add part with only SerialNo to Vehicle
        else if (serialNumberOptional.isPresent()) {
            part = partRepository.findBySerialNumberAndManufacturerAndPartType(serialNumber, manufacturer, partType);
            if (part.isEmpty()) {
                throw new PartException(evMessageBundle.getMessage("PART_SERIAL_NOT_FOUND", serialNumber));
            }
        } else {
            throw new PartException(evMessageBundle.getMessage("SERIAL_BATCH_EMPTY"));
        }
        Optional<Long> vehicleId = vehicleRepository.findVehicleByPartId(part.get().getId());
        if (vehicleId.isPresent()) {
            if (vehicleId.get().equals(vehicle.getId())) {
                throw new PartException(evMessageBundle.getMessage("PART_ALREADY_PRESENT_IN_VEHICLE", vehicleId.get()));
            } else {
                throw new PartException(evMessageBundle.getMessage("PART_ALREADY_PRESENT_IN_DIFFERENT_VEHICLE", vehicleId.get()));
            }
        }
        PartModel partModel = part.get().getPartModel();
        boolean isPartModelPresent = vehicle.getVehicleModel().getPartModels().stream().anyMatch(e -> e.getId().equals(partModel.getId()));
        if (isPartModelPresent) {
            boolean isPartTypePresent = vehicle.getVehicleParts().stream()
                    .anyMatch(e -> e.getPartType().equals(part.get().getPartType()));
            if (part.get().getPartType().equals(PartType.GSM)) {
                vehicle.setImei(part.get().getSerialNumber());
            }
            if (isPartTypePresent) {
                //remove old part
                vehicle.getVehicleParts().removeIf(e -> e.getPartType().equals(part.get().getPartType()));
                Optional<PartReplacementLog> endLog = partReplacementLogRepository.findByVehicleIdAndPartTypeAndEndTimeNull(vehicle.getId(), part.get().getPartType());
                //updating old log
                endLog.ifPresent(log -> {
                    log.setEndTime(Instant.now());
                    log.setUser(user);
                    partReplacementLogRepository.save(log);
                });
            }
            //add new part
            vehicle.getVehicleParts().add(part.get());
            //updating new log
            PartReplacementLog startLog = new PartReplacementLog();
            startLog.setVehicle(vehicle);
            startLog.setPart(part.get());
            startLog.setImei(vehicle.getImei());
            startLog.setPartType(part.get().getPartType());
            startLog.setUser(user);
            partReplacementLogRepository.save(startLog);
            vehicleCacheRefreshService.evictImeiToVariantKeyCache(vehicle);
            vehicleCacheRefreshService.evictImeiToVehicleCache(vehicle);
        } else {
            throw new PartException(evMessageBundle.getMessage("PART_MODEL_NOT_FOUND_IN_VEHICLE_MODEL"));
        }
        vehicleRepository.save(vehicle);
    }


    @Transactional
    @Override
    public void updateVehicleRegistrationDetails(VehicleRegistrationDto vehicleRegistrationDto) {
        existsByImei(vehicleRegistrationDto.getImei());
        Vehicle vehicle = getVehicleByAnyId(vehicleRegistrationDto.getImei());

        Optional<VehicleRegistrationDetails> existingVehicleRegistrationDetails = vehicleRegistrationDetailsRepository.findByVehicleId(
                vehicle.getId());
        VehicleRegistrationDetails vehicleRegistrationDetails = new VehicleRegistrationDetails();
        vehicleRegistrationDetails.setVehicle(vehicle);
        vehicleRegistrationDetails.setRegistrationNumber(
                vehicleRegistrationDto.getRegistrationNumber());
        vehicleRegistrationDetails.setTemporaryRegistrationNumber(
                vehicleRegistrationDto.getTemporaryRegistrationNumber());
        vehicleRegistrationDetails.setSaleDate(vehicleRegistrationDto.getSaleDate());
        if (existingVehicleRegistrationDetails.isPresent()) {
            existingVehicleRegistrationDetails.get().setRegistrationNumber(
                    vehicleRegistrationDto.getRegistrationNumber());
            existingVehicleRegistrationDetails.get().setTemporaryRegistrationNumber(
                    vehicleRegistrationDto.getTemporaryRegistrationNumber());
            existingVehicleRegistrationDetails.get().setSaleDate(vehicleRegistrationDto.getSaleDate());
        } else {
            vehicleRegistrationDetailsRepository.save(vehicleRegistrationDetails);
        }
    }

    @Override
    public Optional<VehicleDto> find(Optional<String> identifier)
            throws VehicleException, VehicleModelException {
        if (identifier.isPresent()) {
            Optional<Vehicle> vehicles = getVehicle(identifier.get());
            return entityToDto(vehicles.stream());
        }

        throw new IllegalArgumentException(evMessageBundle.getMessage("PROVIDE_IMEI_CHASSIS_ID"));

    }

    @Transactional
    @Override
    public void updateVehicleDealership(String identifier, String dealershipName) {
        Vehicle vehicle = getVehicleByAnyId(identifier);
        CustomOrganisation dealership = Optional.ofNullable(customOrganisationRepository.findByName(dealershipName))
                .orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("DEALER_NOT_FOUND", dealershipName)));
        vehicle.setDealership(dealership);
    }

    @Transactional
    @Override
    public void sellVehicle(SellVehicleRequestBody sellVehicleRequestBody, HttpServletRequest request) throws UserNotFoundException {
        CustomOrganisation organisation = null;
        CustomUser user = null;
        if (sellVehicleRequestBody.getOrgName() == null || sellVehicleRequestBody.getOrgName().isBlank()) {
            if (sellVehicleRequestBody.getImeis().size() > 1) {
                throw new IllegalArgumentException(evMessageBundle.getMessage("MULTIPLE_IMEI_B2C"));
            }
            if (sellVehicleRequestBody.getImeis().size() == 0) {
                throw new IllegalArgumentException(evMessageBundle.getMessage("IMEI_LIST_EMPTY"));

            }
            String imei = sellVehicleRequestBody.getImeis().stream().findFirst().get();
            Vehicle vehicle = vehicleRepository.findByImei(imei).orElseThrow(() -> new VehicleNotFoundException("VEHICLE_NOT_FOUND"));
            //B2C- phoneNumber mandatory
            log.info("Registering B2C organisation, to sell vehicle");
            CustomOrganisationDto customOrganisationDto = getCustomOrganisationDto(sellVehicleRequestBody, vehicle);

            customOrganisationDto.setUserDetails(sellVehicleRequestBody.getUserDetails());
            Optional<CustomUser> customUser = userRepository.findByPhoneNumber(sellVehicleRequestBody.getUserDetails().getPhoneNumber());

            if (customUser.isPresent()) {

                Optional<CustomOrganisation> userOrg = customOrganisationRepository.findByUser(customUser.get()).stream().filter(e -> e.getOrganisationType().equals(OrganisationType.B2CCUSTOMER)).findFirst();

                if (userOrg.isPresent()) {
                    organisation = userOrg.get();
                }
                user = customUser.get();
            }
            if (customUser.isEmpty() || Optional.ofNullable(organisation).isEmpty()) {
                ResponseEntity<?> responseEntity = organisationController.saveB2COrganisation(customOrganisationDto, request);
                log.info(responseEntity.getBody().toString());
                organisation = Optional.ofNullable(customOrganisationRepository.findByPhoneNumber(customOrganisationDto.getPhoneNumber()))
                        .orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("ORG_NOT_FOUND")));
                user = userService.findByPhoneNumber(sellVehicleRequestBody.getUserDetails().getPhoneNumber());
            }

        } else {
            //B2B or DEALER, org already created
            log.info("Selling vehicle to DEALER/B2B organisation");
            organisation = Optional.ofNullable(customOrganisationRepository.findByName(sellVehicleRequestBody.getOrgName()))
                    .orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("ORG_NOT_FOUND")));
            Set<CustomUser> customUsers = organisation.getUsers();
            //Getting admin user
            CustomOrganisation finalOrganisation = organisation;
            user = customUsers.stream()
                    .filter(user1 -> user1.getEmail().equals(finalOrganisation.getOrganisationProfile().getEmail()))
                    .findFirst().orElseThrow(() -> new UserNotFoundException(evMessageBundle.getMessage("USER_NOT_FOUND")));
        }

        List<VehicleSaleLog> logs = new ArrayList<>();
        for (String imei : sellVehicleRequestBody.getImeis()) {
            existsByImei(imei);
            Vehicle vehicle = getVehicleByAnyId(imei);
            vehicle.setOwner(organisation);
            vehicle.setUser(user);
            if (sellVehicleRequestBody.getOrgName() == null || sellVehicleRequestBody.getOrgName().isBlank()) {
                Optional<Vehicle> existingVehicle = vehicleRepository.findByUser(user).stream().filter(e -> ((CustomOrganisation) e.getOwner()).getOrganisationType().equals(OrganisationType.B2CCUSTOMER) && !e.getImei().equals(imei)).toList().stream().findFirst();
                if (existingVehicle.isPresent()) {
                    throw new UserAlreadyPairedException(evMessageBundle.getMessage("USER_ALREADY_ASSOCIATED_WITH_OTHER"));
                }
            }
            Optional<UserVehicleConnection> userVehicleConnection = userVehicleConnectionRepository.findByUserAndVehicle(user, vehicle).stream().findFirst();
            if (userVehicleConnection.isEmpty()) {
                UserVehicleConnection newConnection = new UserVehicleConnection();
                newConnection.setVehicle(vehicle);
                newConnection.setUser(user);
                newConnection.setStartOn(LocalDateTime.now());
                userVehicleConnectionRepository.save(newConnection);
            }
            //updating old log
            Optional<VehicleSaleLog> oldLog = vehicleSaleLogRepository.findByVehicleIdAndEndTimeNull(vehicle.getId());
            oldLog.ifPresent(log -> {
                log.setEndTime(Instant.now());
                logs.add(log);  // Add old log to the list for bulk saving
            });
            //updating new log
            VehicleSaleLog newLog = new VehicleSaleLog();
            newLog.setVehicle(vehicle);
            newLog.setStartTime(Instant.now());
            newLog.setOwner(organisation);
            newLog.setUser(user);
            logs.add(newLog);  // Add new log to the list
        }
        vehicleSaleLogRepository.saveAll(logs);
    }

    private CustomOrganisationDto getCustomOrganisationDto(SellVehicleRequestBody sellVehicleRequestBody, Vehicle vehicle) {


        CustomOrganisation organisation = (CustomOrganisation) vehicle.getManufacturer();
        String  urlSlug = organisation.getUrlSlug();


        B2cUserRegistrationDto userRegistrationDTO = sellVehicleRequestBody.getUserDetails();
        CustomOrganisationDto customOrganisationDto = new CustomOrganisationDto();
        customOrganisationDto.setName(String.join("/",
                Objects.requireNonNullElse(userRegistrationDTO.getFirstName(), ""),
                Objects.requireNonNullElse(userRegistrationDTO.getLastName(), ""),
                Objects.requireNonNullElse(userRegistrationDTO.getPhoneNumber(), ""),
                Objects.requireNonNullElse(userRegistrationDTO.getEmail(), "")));
        customOrganisationDto.setEmail(userRegistrationDTO.getEmail());
        customOrganisationDto.setPhoneNumber(userRegistrationDTO.getPhoneNumber());
        customOrganisationDto.setDescription("B2C");
        customOrganisationDto.setOrganisationType(OrganisationType.B2CCUSTOMER);
        customOrganisationDto.setUrlSlug(urlSlug);
        customOrganisationDto.setType(new ArrayList<>());
        customOrganisationDto.setLinkedOrganisationId(new ArrayList<>());
        AddressDto address = new AddressDto();
        address.setAddressLine1(" ");
        address.setAddressLine2(" ");
        address.setCity(" ");
        address.setAdminArea(" ");
        address.setPostalCode(" ");
        address.setCountry(" ");
        customOrganisationDto.setAddress(address);
        return customOrganisationDto;
    }

    @Override
    public boolean existsByImei(String imei) {
        boolean exists = vehicleRepository.existsByImei(imei);
        if (!exists) {
            throw new VehicleNotFoundException(evMessageBundle.getMessage("VEHICLE_NOT_FOUND_WITH_IMEI", imei));
        }
        return true;
    }

    @Override
    @Transactional
    public Float getLatestOdometerReadingThroughMotorSpeed(String identifier) {
        Vehicle vehicle = getVehicleByAnyId(identifier);
        String imei = vehicle.getImei();
        Float diameter = 0.456f;
        Optional<PartModelAttributeDto> partModelAttributeProjection = Optional.ofNullable(vehicleCacheRetrievalService.getAttribute("rearTyreDiameter", PartType.REAR_TYRE.name(), vehicle));
        // Optional<PartModelAttribute> wheelModelAttribute = partModelService.getAttribute(vehicle.getVehicleParts(), PartType.WHEEL, "rearTyreDiameter");
        diameter = partModelAttributeProjection.isPresent() ? Float.parseFloat(partModelAttributeProjection.get().getValue()) : diameter;
        Instant odometerLastUpdatedAt = vehicle.getOdometerLastUpdatedAt();
        if (Optional.ofNullable(odometerLastUpdatedAt).isEmpty()) {
            odometerLastUpdatedAt = Instant.parse("2020-01-01T00:00:00.00Z");
        }
        Float distanceTraveled = 0f;
        List<SpeedDto> speedDtoList = motorDataRepository.getDistanceTravelled(imei,
                odometerLastUpdatedAt);
        Instant latestTelemetryTimestamp = vehicleDataRepository.getLatestTelemetryTimestamp(imei);
        vehicle.setOdometerLastUpdatedAt(latestTelemetryTimestamp);

        if (speedDtoList.size() > 0) {
            vehicle.setOdometerLastUpdatedAt(speedDtoList.get(speedDtoList.size() - 1).getTimestamp());
            distanceTraveled = getDistanceTravelled(speedDtoList, odometerLastUpdatedAt, diameter);
        }

        Float currentDistanceTraveled =
                Optional.ofNullable(vehicle.getTotalDistanceTraveled()).isEmpty() ? 0
                        : vehicle.getTotalDistanceTraveled();
        if (distanceTraveled != null) {
            log.info(" IMEI NO. " + imei + " Total distance travelled: " + (currentDistanceTraveled + distanceTraveled));
            vehicle.setTotalDistanceTraveled(currentDistanceTraveled + distanceTraveled);
        }
        vehicleRepository.save(vehicle);
        return currentDistanceTraveled + distanceTraveled;
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void updateVehicleRunningMetricsAndOdometerInVehicleLatestData(String imei, Instant vehicleRunningMetricsAt, DataFrequencyPlanDetails dataFrequencyPlanDetails) {
        final AtomicReference<Double> distanceTraveled = new AtomicReference<>(0d);
        log.debug("IMEI, {}, updateVehicleRunningMetricsAndOdometerInVehicleLatestData at timestamp : {} dataFrequencyPlanDetails : {}", imei, vehicleRunningMetricsAt, dataFrequencyPlanDetails.getComputationFrequency());

        // got vehicle from imei else give message
        Vehicle vehicle = vehicleRepository.findByImei(imei)
                .orElseThrow(() -> new VehicleException(evMessageBundle.getMessage("VEHICLE_NOT_FOUND")));


        try {
            //data computing frequency for given vehicle
            Short computingFrequency = dataFrequencyPlanDetails.getComputationFrequency();

            //current distance travelled by vehicle, if null assign zero
            Float currentDistanceTraveled = Optional.ofNullable(vehicle.getTotalDistanceTraveled()).orElse(0f);

            // initialize vehicle running metrics date if null
            Instant odometerLastUpdatedAt = null;
            if (Optional.ofNullable(vehicle.getOdometerLastUpdatedAt()).isEmpty()) {
                odometerLastUpdatedAt = Instant.parse("2020-01-01T00:00:00.00Z");
                vehicle.setOdometerLastUpdatedAt(odometerLastUpdatedAt);
            } else {
                odometerLastUpdatedAt = vehicle.getOdometerLastUpdatedAt();
            }

            String chunkFrequencyInString = computingFrequency + " seconds";

            //get all location data points along with di_motion and di_ignition true from current time to last odometer updated at
            List<RunningMetricsDataProjection> tripLocationProjections = this.getRunningMetricsData(vehicle.getImei(), odometerLastUpdatedAt, vehicleRunningMetricsAt);

            //size of all location data points
            int tripLocationProjectionsLength = tripLocationProjections.size();
            if (vehicle.getOdometerLastUpdatedAt().isBefore(vehicleRunningMetricsAt)) {
                vehicle.setOdometerLastUpdatedAt(vehicleRunningMetricsAt);
            }
            vehicleRepository.saveAndFlush(vehicle);
            if (tripLocationProjectionsLength == 0) {
                return;
            }

            //separated latest data points chunk to older data points
            List<RunningMetricsDataProjection> incomingLocationProjections = new ArrayList<>();
            List<RunningMetricsDataProjection> outgoingLocationProjections = new ArrayList<>();
            Instant lastChunkLocationDataStartDate = vehicleRunningMetricsAt.minusSeconds(computingFrequency - 1);
            tripLocationProjections.forEach(e -> {
                if (e.getTimestamp().isAfter(lastChunkLocationDataStartDate)) {
                    incomingLocationProjections.add(e);
                } else {
                    outgoingLocationProjections.add(e);
                }
            });

            //get chunk of latest packet data as Map form
            Map<Instant, List<RunningMetricsDataProjection>> incomingLocationProjectionsInChunks = getChunksOfLocationDataProjections(incomingLocationProjections, computingFrequency);

            //get chunk of older data as Map form in chunks
            Map<Instant, List<RunningMetricsDataProjection>> outgoingLocationProjectionsInChunks = getChunksOfLocationDataProjections(outgoingLocationProjections, computingFrequency);

            PartModelAttributeDto fullCapacityDto = vehicleCacheRetrievalService.getAttribute(fullCapacityAttributeName, PartType.BATTERY.name(), vehicle);

            List<VehicleRunningMetrics> finalVehicleRunningMetricsUpdateOrInsert = new ArrayList<>();
            List<Instant> outgoingInstants = outgoingLocationProjectionsInChunks.keySet().stream().toList();
            List<VehicleRunningMetrics> outgoingVehicleRunningMetrics = vehicleRunningMetricsRepository.findByTelemetryIdxTimestampInAndTelemetryIdxImeiOrderByTelemetryIdxTimestampAsc(outgoingInstants, imei);
            Map<Instant, VehicleRunningMetrics> outgoingVehicleRunningMetricsMap = createMapFromMetrics(outgoingVehicleRunningMetrics);


            outgoingLocationProjectionsInChunks.forEach((key, value) -> {
                Optional<VehicleRunningMetrics> vehicleRunningMetrics = Optional.ofNullable(outgoingVehicleRunningMetricsMap.get(key));
                vehicleRunningMetrics.ifPresent(runningMetrics -> distanceTraveled.updateAndGet(v -> v - runningMetrics.getDistanceTravelled()));
                VehicleRunningMetrics vehicleRunningMetricsUpdated = saveVehicleRunningMetric(key, value, vehicleRunningMetrics.orElse(new VehicleRunningMetrics()), vehicle, computingFrequency, fullCapacityDto);
                if (Optional.ofNullable(vehicleRunningMetricsUpdated.getDistanceTravelled()).isPresent()) {
                    distanceTraveled.updateAndGet(v -> v + vehicleRunningMetricsUpdated.getDistanceTravelled());
                    finalVehicleRunningMetricsUpdateOrInsert.add(vehicleRunningMetricsUpdated);
                }
            });


            //update vehicle_running_metrics's latest packet data points
            incomingLocationProjectionsInChunks.forEach((key, value) -> {
                VehicleRunningMetrics vehicleRunningMetrics = saveVehicleRunningMetric(key, value, new VehicleRunningMetrics(), vehicle, computingFrequency, fullCapacityDto);
                distanceTraveled.updateAndGet(v -> v + vehicleRunningMetrics.getDistanceTravelled());
                finalVehicleRunningMetricsUpdateOrInsert.add(vehicleRunningMetrics);

            });

            vehicleRunningMetricsRepository.saveAll(finalVehicleRunningMetricsUpdateOrInsert);


            if (tripLocationProjectionsLength > 0) {
                //update vehicle odometer and date
                vehicle.setTotalDistanceTraveled(currentDistanceTraveled + distanceTraveled.get().floatValue());

            }


            // vehicle latest data update
            Optional<VehicleLatestData> vehicleLatestDataOptional = vehicleLatestDataRepository.findByImei(imei);
            if (vehicleLatestDataOptional.isEmpty()) {
                log.debug("vehicle latest data is empty for IMEI : {}", imei);
            } else if (tripLocationProjectionsLength > 0) {
                VehicleLatestData vehicleLatestData = vehicleLatestDataOptional.get();
                vehicleLatestData.setCalOdometer(Optional.ofNullable(vehicleLatestData.getCalOdometer()).orElse(0f) + distanceTraveled.get().floatValue());
                vehicleLatestData.setCalOdometerUpdatedAt(tripLocationProjections.get(tripLocationProjectionsLength - 1).getTimestamp());
                vehicleLatestDataRepository.save(vehicleLatestData);

            }
        } catch (Exception e) {
            log.error("Error in updating vehicle running metrics and odometer for IMEI: {} at timestamp: {} ",e, imei, vehicleRunningMetricsAt, e);
            throw new VehicleException(evMessageBundle.getMessage("VEHICLE_RUNNING_METRICS_UPDATE_ERROR", imei, e.getMessage()));
        }

    }

    private List<RunningMetricsDataProjection> getRunningMetricsData(String imei, Instant odometerLastUpdatedAt, Instant vehicleRunningMetricsAt) {

        try{
        Instant bufferTime = Instant.now().minus(5,ChronoUnit.DAYS);
        List<BatteryDataProjection> batteryData = telemetryBatteryRepository.findSocAndRemainingCapacityByImeiAndCreatedBetween(imei, odometerLastUpdatedAt, vehicleRunningMetricsAt,bufferTime);
        List<MotorTelemetryDataProjection> motorTelemetryData = motorDataRepository.findDriveModeByImeiAndTimestampBetween(imei, odometerLastUpdatedAt, vehicleRunningMetricsAt,bufferTime);
        List<LocationDataProjection> locationData = locationDataRepository.findLocationByImeiAndCreatedOnBetween(imei, odometerLastUpdatedAt, vehicleRunningMetricsAt,bufferTime);

        Map<Instant, LocationDataProjection> locationMap = locationData.stream()
                .collect(Collectors.toMap(TimestampProjection::getTimestamp, l -> l));

        Map<Instant, BatteryDataProjection> batteryMap = batteryData.stream()
                .collect(Collectors.toMap(TimestampProjection::getTimestamp, b -> b));

        Map<Instant, BatteryDataProjection> previousBatteryMap = batteryData.stream()
                .collect(Collectors.toMap(TimestampProjection::getTimestamp,
                                b->telemetryBatteryRepository
                                        .findBatteryDataById(b.getTimestamp().minus(10,ChronoUnit.SECONDS),imei,bufferTime)
                ));


        Map<Instant, MotorTelemetryDataProjection> motorMap = motorTelemetryData.stream()
                .collect(Collectors.toMap(TimestampProjection::getTimestamp, m -> m));


        Set<Instant> commonKeys = new HashSet<>(locationMap.keySet());
        commonKeys.retainAll(batteryMap.keySet());
        commonKeys.retainAll(motorMap.keySet());
        commonKeys.retainAll(previousBatteryMap.keySet());

        return commonKeys.stream()
                .map(key -> {

                    LocationDataProjection location = locationMap.get(key);
                    BatteryDataProjection battery = batteryMap.get(key);
                    MotorTelemetryDataProjection motor = motorMap.get(key);
                    BatteryDataProjection previousBattery = previousBatteryMap.get(key);

                    return new RunningMetricsDataDto(
                            imei,
                            key,
                            battery != null ? battery.getSoc() : null,
                            battery != null ? battery.getRemainingCapacity() : null,
                            location != null ? location.getLatitude() : null,
                            location != null ? location.getLongitude() : null,
                            motor != null ? motor.getMotorDrivingMode() : null,
                            previousBattery != null ? (double) (previousBattery.getSoc() - battery.getSoc()) : null,
                            location != null ? location.getSpeed() : null,
                            previousBattery != null ? (double) (previousBattery.getRemainingCapacity() - battery.getRemainingCapacity()) : null
                    );
                })
                .map(RunningMetricsDataProjection.class::cast)
                .toList();
    } catch (Exception e) {
        log.error("Error in fetching running metrics data", e);
        return new ArrayList<>();
    }
    }

    public static Map<Instant, VehicleRunningMetrics> createMapFromMetrics(List<VehicleRunningMetrics> outgoingVehicleRunningMetrics) {
        return outgoingVehicleRunningMetrics.stream()
                .collect(Collectors.toMap(
                        metrics -> metrics.getTelemetryIdx().getTimestamp(),
                        metrics -> metrics
                ));
    }

    @Transactional
    VehicleRunningMetrics saveVehicleRunningMetric(Instant timestamp, List<RunningMetricsDataProjection> projections, VehicleRunningMetrics vehicleRunningMetrics, Vehicle vehicle, int computationFrequency, PartModelAttributeDto partModelAttributeDto) {
        Map<String, Integer> frequencyDriveModeMap = new HashMap<>();
        Double chunkDistance = 0.0;
        Float chunkMaxSpeed = 0f;
        Float chunkAvgSpeed = 0f;
        Double discharge = null;
        Double calDischarge = null;
        Float soc = null;
        Double calSoc = null;
        int countOfSpeedChunkData = 0;
        int chunkDataSize = projections.size();

        for (int index = 0; index < chunkDataSize - 1; index++) {

            if (Duration.between(projections.get(index).getTimestamp(), projections.get(index + 1).getTimestamp()).getSeconds() <= computationFrequency) {
                chunkDistance += iVehicleLocationService.getDistanceFromLocation(projections.get(index).getLatitude(), projections.get(index).getLongitude(), projections.get(index + 1).getLatitude(), projections.get(index + 1).getLongitude());

                Float tempSpeed = projections.get(index).getSpeed();
                if (tempSpeed != null && tempSpeed > chunkMaxSpeed) {
                    chunkMaxSpeed = projections.get(index).getSpeed();
                }
                if (tempSpeed != null) {
                    chunkAvgSpeed += tempSpeed;
                    countOfSpeedChunkData++;
                }
                if (projections.get(index).getMotorDrivingMode() != null) {
                    frequencyDriveModeMap.put(projections.get(index).getMotorDrivingMode(), frequencyDriveModeMap.getOrDefault(projections.get(index).getMotorDrivingMode(), 0) + 1);
                }
            }
            if (Optional.ofNullable(projections.get(index).getDischarge()).isPresent()) {
                discharge = this.formatDoubleToTwoDecimal(projections.get(index).getDischarge());
            }

            if (Optional.ofNullable(projections.get(index).getCalRemainingCapacityDiff()).isPresent()) {
                Double remainingCapacityDiff = projections.get(index).getCalRemainingCapacityDiff();
                Double fullCapacity = Double.valueOf(partModelAttributeDto.getValue());
                calDischarge = this.formatDoubleToTwoDecimal((remainingCapacityDiff / fullCapacity) * 100);

            }
            if (Optional.ofNullable(projections.get(index).getRemainingCapacity()).isPresent()) {
                calSoc = getCalculatedSoc(projections.get(chunkDataSize - 1).getRemainingCapacity(), partModelAttributeDto);
                calSoc = Optional.ofNullable(calSoc).isPresent() ? this.formatDoubleToTwoDecimal(calSoc) : null;

            }
            if (Optional.ofNullable(projections.get(index).getSoc()).isPresent()) {
                {
                    soc = projections.get(index).getSoc();
                }
            }
        }
        // calculating speed for the last index chunk
        if (chunkDataSize > 0) {
            int lastIndex = chunkDataSize - 1;
            Float lastSpeed = projections.get(lastIndex).getSpeed();
            if (lastSpeed != null) {
                if (lastSpeed > chunkMaxSpeed) {
                    chunkMaxSpeed = lastSpeed;
                }
                chunkAvgSpeed += lastSpeed;
                countOfSpeedChunkData++;
            }
        }

        if (chunkAvgSpeed > 0) {
            chunkAvgSpeed = getAvgSpeed(chunkAvgSpeed, countOfSpeedChunkData);
            if (chunkMaxSpeed == 0) {
                chunkMaxSpeed = chunkAvgSpeed;
            }
        }

        String driveModeString = frequencyDriveModeMap.entrySet()
                .stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(null);

        if (chunkDistance > 0) {
            if (vehicleRunningMetrics.getTelemetryIdx() != null) {
                vehicleRunningMetrics.setTelemetryIdx(new TelemetryIdx(timestamp, vehicle.getImei()));
            }
            vehicleRunningMetrics.setDistanceTravelled(chunkDistance.floatValue());
        }
        vehicleRunningMetrics.setSoc(soc);
        vehicleRunningMetrics.setCalculatedSoc(calSoc);
        vehicleRunningMetrics.setDischarge(discharge);
        vehicleRunningMetrics.setCalDischarge(calDischarge);
        vehicleRunningMetrics.setMaxSpeed(chunkMaxSpeed);
        vehicleRunningMetrics.setVehicle(vehicle);
        vehicleRunningMetrics.setAvgSpeed(Optional.ofNullable(chunkAvgSpeed).isPresent() ? Math.round(chunkAvgSpeed * 100) / 100.0f : null);
        Optional<DriveModeSpeed> driveModeSpeed = Optional.empty();
        if (Optional.ofNullable(driveModeString).isPresent()) {
            driveModeSpeed = vehicle.getVehicleModel().getDriveModes().stream().filter(e -> e.getDriveMode().getName().equals(driveModeString)).findFirst();
        }

        vehicleRunningMetrics.setDriveMode(driveModeSpeed.map(DriveModeSpeed::getDriveMode).orElse(null));
        if (Optional.ofNullable(vehicleRunningMetrics.getTelemetryIdx()).isEmpty() || Optional.ofNullable(vehicleRunningMetrics.getTelemetryIdx().getTimestamp()).isEmpty() || Optional.ofNullable(vehicleRunningMetrics.getTelemetryIdx().getImei()).isEmpty()) {
            TelemetryIdx telemetryIdx = new TelemetryIdx();
            telemetryIdx.setImei(vehicle.getImei());
            telemetryIdx.setTimestamp(timestamp);
            vehicleRunningMetrics.setTelemetryIdx(telemetryIdx);
        }

        log.debug(vehicleRunningMetrics.toString());
        return vehicleRunningMetrics;
    }

    private Double formatDoubleToTwoDecimal(Double value) {
        DecimalFormat decimalFormatter = new DecimalFormat("#.##");
        return Double.valueOf(decimalFormatter.format(value));
    }

    private Map<Instant, List<RunningMetricsDataProjection>> getChunksOfLocationDataProjections(List<RunningMetricsDataProjection> projections, int chunkSizeInSeconds) {
        Map<Instant, List<RunningMetricsDataProjection>> map = new LinkedHashMap<>();
        Instant previousChunkStart = null; // To track the previous chunk's start

        List<RunningMetricsDataProjection> currentChunkProjections = new ArrayList<>(); // To accumulate projections for the current chunk
        RunningMetricsDataProjection lastChunkEndProjection = null;
        for (RunningMetricsDataProjection projection : projections) {
            long seconds = projection.getTimestamp().getEpochSecond();

            // Calculate the start of the current chunk
            Instant chunkStart = getLocationDataComputationFrequencyChunkStartOrEndInstant(seconds, chunkSizeInSeconds);
            // Calculate the end of the current chunk based on chunkStart
            Instant chunkEnd = chunkStart.plusSeconds(chunkSizeInSeconds);

            // If the current projection belongs to a new chunk, finalize the previous chunk
            if (previousChunkStart != null && !chunkStart.equals(previousChunkStart)) {
                // Store accumulated data in the map using chunkEnd as the key
                map.put(previousChunkStart.plusSeconds(chunkSizeInSeconds), new ArrayList<>(currentChunkProjections));
                lastChunkEndProjection = currentChunkProjections.get(currentChunkProjections.size() - 1);
                // Clear the list for the next chunk
                currentChunkProjections.clear();
            }
            if (currentChunkProjections.isEmpty() && Optional.ofNullable(lastChunkEndProjection).isPresent()) {
                currentChunkProjections.add(lastChunkEndProjection);
            }
            // Add the current projection to the list of projections for the current chunk
            currentChunkProjections.add(projection);
            previousChunkStart = chunkStart; // Update the previous chunk start
        }

        // Handle the last chunk (add remaining data to the map)
        if (!currentChunkProjections.isEmpty()) {
            map.put(previousChunkStart.plusSeconds(chunkSizeInSeconds), currentChunkProjections); // Use the end time of the last chunk
        }

        return map.entrySet()
                .stream()
                .sorted(Map.Entry.comparingByKey())  // Sort by key (Instant)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue, // Merge function (not needed for LinkedHashMap)
                        LinkedHashMap::new // Collect into LinkedHashMap to maintain insertion order
                ));
    }

    private Instant getLocationDataComputationFrequencyChunkStartOrEndInstant(long seconds, int chunkSizeInSeconds) {
        // Calculate the start of the chunk (nearest lower multiple of chunkSizeInSeconds)
        long chunkSeconds = (seconds / chunkSizeInSeconds) * chunkSizeInSeconds;
        return Instant.ofEpochSecond(chunkSeconds).truncatedTo(ChronoUnit.SECONDS);

    }


    @Override
    @Transactional
    public Float getLatestOdometerReadingThroughLatAndLong(String imei) {
        Double distanceTraveled = 0d;
        Vehicle vehicle = getVehicleByAnyId(imei);
        Float currentDistanceTraveled = Optional.ofNullable(vehicle.getTotalDistanceTraveled()).orElse(0f);
        Instant odometerLastUpdatedAt = vehicle.getOdometerLastUpdatedAt();
        if (Optional.ofNullable(odometerLastUpdatedAt).isEmpty()) {
            odometerLastUpdatedAt = Instant.parse("2020-01-01T00:00:00.00Z");
        }
        List<RunningMetricsDataProjection> tripLocationProjections = locationDataRepository.findByTelemetryIdxImeiAndTimestampBetweenOrderByTelemetryIdxTimestampAsc(vehicle.getImei(), odometerLastUpdatedAt, Instant.now(), "10 seconds");
        int tripLocationProjectionsLength = tripLocationProjections.size();
        Float maxSpeed = 0f;
        Float avgSpeed = 0f;
        int countOfSpeedData = 0;

        // get avg speed by getAvgSpeed Method, parameter distance is in km and time is in sec. it will give you avgSpeed in km/hr
        Map<String, Integer> frequencyDriveModeMap = new HashMap<>();
        for (int index = 0; index < tripLocationProjectionsLength - 1; index++) {
            distanceTraveled += iVehicleLocationService.getDistanceFromLocation(tripLocationProjections.get(index).getLatitude(), tripLocationProjections.get(index).getLongitude(), tripLocationProjections.get(index + 1).getLatitude(), tripLocationProjections.get(index + 1).getLongitude());
            Float tempSpeed = tripLocationProjections.get(index).getSpeed();
            if (tempSpeed != null && tempSpeed > maxSpeed) {
                maxSpeed = tripLocationProjections.get(index).getSpeed();
            }
            if (tempSpeed != null) {
                avgSpeed += tempSpeed;
                countOfSpeedData++;
            }
            if (tripLocationProjections.get(index).getMotorDrivingMode() != null) {
                frequencyDriveModeMap.put(tripLocationProjections.get(index).getMotorDrivingMode(), frequencyDriveModeMap.getOrDefault(tripLocationProjections.get(index).getMotorDrivingMode(), 0) + 1);
            }
        }
        if (avgSpeed > 0) {
            avgSpeed = getAvgSpeed(avgSpeed, countOfSpeedData);
            if (maxSpeed == 0) {
                maxSpeed = avgSpeed;
            }
        }
        String driveModeString = frequencyDriveModeMap.entrySet()
                .stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(null);
        log.info(" IMEI : {}, Avg Speed : {}, Max Speed : {}, frequent drive mde : {}", imei, avgSpeed, maxSpeed, driveModeString);

        if (distanceTraveled > 0) {
            PartModelAttributeDto fullCapacityDto = vehicleCacheRetrievalService.getAttribute(fullCapacityAttributeName, PartType.BATTERY.name(), vehicle);
            vehicle.setTotalDistanceTraveled(currentDistanceTraveled + distanceTraveled.floatValue());
            vehicle.setOdometerLastUpdatedAt(tripLocationProjections.get(tripLocationProjectionsLength - 1).getTimestamp());
            //Updating vehicle_daily_distance
            VehicleRunningMetrics vehicleRunningMetrics = new VehicleRunningMetrics();
            vehicleRunningMetrics.setTelemetryIdx(new TelemetryIdx(tripLocationProjections.get(tripLocationProjectionsLength - 1).getTimestamp(), imei));
            vehicleRunningMetrics.setDistanceTravelled(distanceTraveled.floatValue());
            vehicleRunningMetrics.setVehicle(vehicle);

//            Double calculatedSoc = getCalculatedSoc(vehicle, tripLocationProjections.get(tripLocationProjectionsLength - 1).getTimestamp(), fullCapacityDto);
//            vehicleRunningMetrics.setCalculatedSoc(calculatedSoc);
            VehicleRunningMetrics latestVehicleRunningMetrics = vehicleRunningMetricsRepository.findFirstByTelemetryIdxImeiOrderByTelemetryIdxTimestampDesc(imei);
            log.debug("latestVehicleRunningMetrics {}", latestVehicleRunningMetrics);
//            vehicleRunningMetrics.setCalDischarge(getCalDischarge(imei, calculatedSoc, latestVehicleRunningMetrics));

            //taking the average of soc from vehicle_battery_data for 10 seconds
            Float socAverage = telemetryBatteryRepository.getAverageSocByImeiAndTimestampBetween(imei, tripLocationProjections.get(tripLocationProjectionsLength - 1).getTimestamp().minusSeconds(10), tripLocationProjections.get(tripLocationProjectionsLength - 1).getTimestamp());
            vehicleRunningMetrics.setSoc(socAverage);
            log.debug("socAverage {} for the vehicle {}", socAverage, imei);

//            vehicleRunningMetrics.setDischarge(getDischarge(imei, socAverage, latestVehicleRunningMetrics));
            vehicleRunningMetrics.setMaxSpeed(maxSpeed);
            vehicleRunningMetrics.setAvgSpeed(Optional.ofNullable(avgSpeed).isPresent() ? Math.round(avgSpeed * 100) / 100.0f : null);
            Optional<DriveModeSpeed> driveModeSpeed = Optional.empty();
            if (Optional.ofNullable(driveModeString).isPresent()) {
                driveModeSpeed = vehicle.getVehicleModel().getDriveModes().stream().filter(e -> e.getDriveMode().getName().equals(driveModeString)).findFirst();
            }

            vehicleRunningMetrics.setDriveMode(driveModeSpeed.isPresent() ? driveModeSpeed.get().getDriveMode() : null);
            vehicleRunningMetricsRepository.save(vehicleRunningMetrics);
            log.info("Updated Odometer of IMEI NO. " + imei + " Total distance travelled: " + (currentDistanceTraveled + distanceTraveled));
            vehicleRepository.save(vehicle);
            Optional<VehicleLatestData> vehicleRecentState = vehicleLatestDataRepository.findById(vehicle.getId());

            vehicleRecentState.get().setCalOdometer(Optional.ofNullable(vehicleRecentState.get().getCalOdometer()).orElse(0f) + distanceTraveled.floatValue());
            vehicleRecentState.get().setCalOdometerUpdatedAt(tripLocationProjections.get(tripLocationProjectionsLength - 1).getTimestamp());
        }

        return currentDistanceTraveled + distanceTraveled.floatValue();
    }

    Float getAvgSpeed(Float totalSpeed, int count) {
        return totalSpeed / count;
    }


    Double getCalculatedSoc(Integer remainingCapacity, PartModelAttributeDto partModelAttributeDto) {
        Double calculatedSoc = null;
        if (partModelAttributeDto != null && Optional.ofNullable(remainingCapacity).isPresent()) {
            Double fullCapacity = Double.valueOf(partModelAttributeDto.getValue());
            calculatedSoc = (remainingCapacity / fullCapacity) * 100;
        }
        return calculatedSoc;
    }


    @Override
    public ExecutiveMetaDataResponseDto getOrganisationMetaDataByOrganisation(CustomOrganisation organisation) {
        DecimalFormat df = new DecimalFormat("#.##");
        VehicleMetaData vehicleMetaData = vehicleRepository.getVehicleMetaData(organisation.getId());
        List<VehicleTestMetaData> vehicleTestMetaData = vehicleTestRepository.getvehicleTestMetaData(organisation.getId(), TestStatus.COMPLETED.toString());
        Pageable sortedByDesc =
                PageRequest.of(0, 10, Sort.by("distance").descending());
        List<VehicleDistanceDataDto> topRunningVehicle = vehicleRepository.getTopTenRunningVehicleByAscOrDesc(organisation.getId(), organisation.getId(), sortedByDesc).stream()
                .map(e -> new VehicleDistanceDataDto(e.getVehChassisNo(), e.getVehRegNo(), e.getVehId(), e.getImei(), Float.valueOf(df.format(e.getDistance())))).toList();
        Pageable sortedByAsc =
                PageRequest.of(0, 10, Sort.by("distance").ascending());
        List<VehicleDistanceDataDto> leastRunningVehicle = vehicleRepository.getTopTenRunningVehicleByAscOrDesc(organisation.getId(), organisation.getId(), sortedByAsc).stream()
                .map(e -> new VehicleDistanceDataDto(e.getVehChassisNo(), e.getVehRegNo(), e.getVehId(), e.getImei(), Float.valueOf(df.format(e.getDistance()))))
                .toList();
        ExecutiveMetaDataResponseDto response = new ExecutiveMetaDataResponseDto();
        response.setTotalDistance(vehicleMetaData.getTotalDistance());
        response.setTotalVehicles(vehicleMetaData.getVehicleCount());
        response.setMaxRunning(!topRunningVehicle.isEmpty() ? topRunningVehicle.get(0).getDistance() : 0);
        response.setMinRunning(!leastRunningVehicle.isEmpty() ? leastRunningVehicle.get(0).getDistance() : 0);
        for (VehicleTestMetaData testMetaData : vehicleTestMetaData) {
            if (testMetaData.getTestType().equals(TestTypeName.CONNECTIVITY)) {
                response.setTotalHeartBeatTest(testMetaData.getTestCount());
            } else if (testMetaData.getTestType().equals(TestTypeName.DYNO)) {
                response.setTotalDynoTest(testMetaData.getTestCount());
            }
        }
        response.setTotalTestRides
                (vehicleTestRepository.getTotalVehicleTestCount(organisation.getId(), TestStatus.COMPLETED.toString(), TestRideSummaryPopulationStatus.COMPLETED.toString(), TestTypeName.TEST_RIDE.toString()));
        response.setTopRunningVehicles(topRunningVehicle);
        response.setLeastRunningVehicles(leastRunningVehicle);
        return response;
    }

    @Override
    public ExecutiveMetaDataResponseDto getOrganisationMetaDataByOrganisationBetweenTimestamp(CustomOrganisation organisation, Optional<Long> startTime, Optional<Long> endTime) {
        DecimalFormat df = new DecimalFormat("#.##");
        ExecutiveMetaDataResponseDto response = new ExecutiveMetaDataResponseDto();
        Instant start = Instant.ofEpochMilli(startTime.get());
        Instant end = Instant.ofEpochMilli(endTime.get());
        List<VehicleTestMetaData> vehicleTestMetaData = vehicleTestRepository.getVehicleTestMetaDataBetweenTimestamp(organisation.getId(), TestStatus.COMPLETED.toString(), start, end);
        List<String> orgImei = vehicleRepository.getVehicleIdentifiersByManufacturerIdOrDealershipId(organisation.getId(), organisation.getId()).stream().map(VehicleIdsProjection::getVehImei).toList();
        Long vehiclesCount = vehicleRunningMetricsRepository.getCountOfDistinctImeiTimeBetween(orgImei, start, end);
        Long totalDistance = vehicleRunningMetricsRepository.getTotalDistanceTravelledBetweenTimeAndImeiIn(start, end, orgImei);
        Pageable sortedByDesc =
                PageRequest.of(0, 10, Sort.by("distance").descending());
        List<VehicleDistanceDataDto> topTenRunningVehicles = vehicleRunningMetricsRepository.getTopRunningVehiclesByAscOrDescBetweenTimeAndVehicleIn(start, end, orgImei, sortedByDesc).stream()
                .map(e -> new VehicleDistanceDataDto(e.getVehChassisNo(), e.getVehRegNo(), e.getVehId(), e.getImei(), Float.valueOf(df.format(e.getDistance())))).toList();
        Pageable sortedByAsc =
                PageRequest.of(0, 10, Sort.by("distance").ascending());
        List<VehicleDistanceDataDto> leastTenRunningVehicles = vehicleRunningMetricsRepository.getTopRunningVehiclesByAscOrDescBetweenTimeAndVehicleIn(start, end, orgImei, sortedByAsc).stream()
                .map(e -> new VehicleDistanceDataDto(e.getVehChassisNo(), e.getVehRegNo(), e.getVehId(), e.getImei(), Float.valueOf(df.format(e.getDistance())))).toList();

        response.setTotalVehicles(vehiclesCount);
        response.setTotalDistance(totalDistance);
        response.setLeastRunningVehicles(leastTenRunningVehicles);
        response.setTopRunningVehicles(topTenRunningVehicles);
        response.setMaxRunning(!topTenRunningVehicles.isEmpty() ? topTenRunningVehicles.get(0).getDistance() : 0);
        response.setMinRunning(!leastTenRunningVehicles.isEmpty() ? leastTenRunningVehicles.get(0).getDistance() : 0);

        for (VehicleTestMetaData testMetaData : vehicleTestMetaData) {
            if (testMetaData.getTestType().equals(TestTypeName.CONNECTIVITY)) {
                response.setTotalHeartBeatTest(testMetaData.getTestCount());
            } else if (testMetaData.getTestType().equals(TestTypeName.DYNO)) {
                response.setTotalDynoTest(testMetaData.getTestCount());
            }
        }
        response.setTotalTestRides
                (vehicleTestRepository.getTotalVehicleTestCountBetweenTimestamp(organisation.getId(), TestStatus.COMPLETED.toString(), TestRideSummaryPopulationStatus.COMPLETED.toString(), TestTypeName.TEST_RIDE.toString(), start, end));
        return response;
    }

    @Override
    public MotorDataResponseDto getData(Long from, Long to, CustomOrganisation organisation) {
        Instant startTime = Instant.ofEpochMilli(from);
        Instant endTime = Instant.ofEpochMilli(to);
        log.info("Start time is {}  end time is {} organisation is {}", startTime, endTime, organisation.getId());
        MotorResultProjection telemetryResult = motorDataRepository.findByImeiInAndTimestampGreaterThanAndTimestampLessThan(startTime, endTime, organisation.getId());
        VehicleBatteryProjection vehicleBatteryProjection = telemetryBatteryRepository.findMinMaxByOrgIdAndTimeBetween(startTime, endTime, organisation.getId());
        VehicleMinMaxDataDto minMaxData = getVehicleMinMaxDto(telemetryResult, vehicleBatteryProjection);
        TelemetryThresholdDto averageData = getMotorDataAboveOrBelowThreshold(organisation, startTime, endTime);
        AlertsCountDto alertsCountDto = new AlertsCountDto();
        alertsCountDto.setBatteryAlertCount(alertService.countVehiclesWithAlerts(from, to));
        return new MotorDataResponseDto(minMaxData, averageData, alertsCountDto);
    }

    private VehicleMinMaxDataDto getVehicleMinMaxDto(MotorResultProjection telemetryResultProjection, VehicleBatteryProjection vehicleBatteryProjection) {
        new VehicleMinMaxDataDto();
        return VehicleMinMaxDataDto.builder()
                .maxDcVoltage(telemetryResultProjection.getMaxDcVoltage())
                .minDcVoltage(telemetryResultProjection.getMinDcCurrent())
                .avgDcVoltage(telemetryResultProjection.getAvgDcVoltage())
                .maxDcCurrent(telemetryResultProjection.getMaxDcCurrent())
                .minDcCurrent(telemetryResultProjection.getMinDcCurrent())
                .avgDcCurrent(telemetryResultProjection.getAvgDcCurrent())
                .maxMotorTemp(telemetryResultProjection.getMaxMotorTemp())
                .minMotorTemp(telemetryResultProjection.getMinMotorTemp())
                .avgMotorTemp(telemetryResultProjection.getAvgMotorTemp())
                .maxMcsTemp(telemetryResultProjection.getMaxMcsTemp())
                .minMcsTemp(telemetryResultProjection.getMinMcsTemp())
                .avgMcsTemp(telemetryResultProjection.getAvgMcsTemp())
                .maxBatteryTemp(vehicleBatteryProjection.getMaxBatteryTemp())
                .minBatteryTemp(vehicleBatteryProjection.getMinBatteryTemp())
                .build();
    }


    @Override
    public VehicleSparklingDataDto getSparklingData(Long from, Long to, String interval, Long orgId) {
        Instant startTime = Instant.ofEpochMilli(from);
        Instant endTime = Instant.ofEpochMilli(to);
        VehicleSparklingDataDto data = new VehicleSparklingDataDto();
        log.info("Started at {}", new Date());
        List<SparklingData> sparklingData = motorDataRepository.getSparklingData(interval, startTime, endTime, orgId);
        data.setDcVoltage(sparklingData.stream().map(SparklingData::getDcVoltage).toList());
        data.setDcCurrent(sparklingData.stream().map(SparklingData::getDcCurrent).toList());
        data.setMcsTemp(sparklingData.stream().map(SparklingData::getMcsTemperature).toList());
        data.setMotorTemp(sparklingData.stream().map(SparklingData::getMotorTemperature).toList());
        log.info("Ended at {}", new Date());
        data.setSensor(getBatteryStackSparklingData(interval, startTime, endTime, orgId));
        log.info("Query Ended at {}", new Date());
        return data;
    }


    @Override
    public List<VehicleMinMaxMotorData> getVehicleData(VehicleDataDto vehicleDataDto, CustomOrganisation organisation) {
        List<VehicleMinMaxMotorData> vehicleMinMaxData = new ArrayList<>();
        String sort = vehicleDataDto.getSortDirection();
        Instant startTime = Instant.ofEpochMilli(vehicleDataDto.getStartTime());
        Instant endTime = Instant.ofEpochMilli(vehicleDataDto.getEndTime());
        switch (vehicleDataDto.getFilter().name()) {
            case "DC_VOLTAGE":
                if (sort.equals("asc")) {
                    vehicleMinMaxData = motorDataRepository.getMaxVehicleDataByDcVoltage(organisation.getId(), startTime, endTime, dcVoltageMax);
                } else {
                    vehicleMinMaxData = motorDataRepository.getMinVehicleDataByDcVoltage(organisation.getId(), startTime, endTime, dcVoltageMin);
                }
                break;
            case "DC_CURRENT":
                if (sort.equals("asc")) {
                    vehicleMinMaxData = motorDataRepository.getMaxVehicleDataByDcCurrent(organisation.getId(), startTime, endTime, dcCurrentMax);
                } else {
                    vehicleMinMaxData = motorDataRepository.getMinVehicleDataByDcCurrent(organisation.getId(), startTime, endTime, dcCurrentMin);
                }
                break;
            case "MCS_TEMPERATURE":
                if (sort.equals("asc")) {
                    vehicleMinMaxData = motorDataRepository.getMaxVehicleDataByMcsTemp(organisation.getId(), startTime, endTime, mcsTempMax);
                } else {
                    vehicleMinMaxData = motorDataRepository.getMinVehicleDataByMcsTemp(organisation.getId(), startTime, endTime, mcsTempMin);
                }
                break;
            case "MOTOR_TEMPERATURE":
                if (sort.equals("asc")) {
                    vehicleMinMaxData = motorDataRepository.getMaxVehicleDataByMotorTemp(organisation.getId(), startTime, endTime, motorTempMax);
                } else {
                    vehicleMinMaxData = motorDataRepository.getMinVehicleDataByMotorTemp(organisation.getId(), startTime, endTime, motorTempMin);
                }
                break;
            case "SENSOR":
                if (sort.equals("asc")) {
                    vehicleMinMaxData = motorDataRepository.getSensorMaxData(organisation.getId(), startTime, endTime, vehicleDataDto.getSensorId(), batterySensorTempMax);
                } else {
                    vehicleMinMaxData = motorDataRepository.getSensorMinData(organisation.getId(), startTime, endTime, vehicleDataDto.getSensorId(), batterySensorTempMin);
                }
            default:
                break;
        }

        return vehicleMinMaxData;
    }

    @Override
    public String getVehicleBySerialNumber(String serialNumber) throws VehicleException {
        List<Part> part = partRepository.findBySerialNumber(serialNumber);
        if (part.isEmpty()) {
            throw new PartException(evMessageBundle.getMessage("PART_NOT_FOUND"));
        }
        Vehicle vehicle = vehicleRepository.findByVehicleParts(part.get(0)).orElseThrow(() -> new VehicleException(evMessageBundle.getMessage("VEHICLE_NOT_FOUND")));
        return vehicle.getImei();
    }


    private Map<String, List<Float>> getBatteryStackSparklingData(String interval, Instant from, Instant to, Long orgId) {
        Map<String, List<Float>> list = new HashMap<>();
        List<Long> sensorIds = vehicleDataRepository.findDistinctSensorIds(from, to, orgId);
        log.info("The sensor Ids are " + sensorIds);
        for (Long sensorId : sensorIds) {
            List<Float> sensorData = vehicleDataRepository.getBatteryStackDta(interval, from, to, orgId, sensorId);
            list.put(sensorId.toString(), sensorData);
        }
        return list;
    }


    private TelemetryThresholdDto getMotorDataAboveOrBelowThreshold(CustomOrganisation organisation, Instant from, Instant to) {
        log.info("inside the getMotorDataAboveOrBelowThreshold");
        TelemetryThresholdDto telemetryThresholdDto = new TelemetryThresholdDto();
        Long countOfVehicle = (long) vehicleRepository.findByManufacturerOrDealership(organisation, organisation).size();
        Float aboveAverageVoltage = motorDataRepository.getCountOfDcVoltageAboveAverage(dcVoltageMax, organisation.getId(), from, to);
        log.info("aboveAverageVoltage {}", aboveAverageVoltage);
        Float belowAverageVoltage = motorDataRepository.getCountOfDcVoltageBelowAverage(dcVoltageMin, organisation.getId(), from, to);
        telemetryThresholdDto.setDcVoltage(getPercentile(aboveAverageVoltage, belowAverageVoltage, countOfVehicle));
        Float aboveAverageCurrent = motorDataRepository.getCountOfDcCurrentAboveAverage(dcCurrentMax, organisation.getId(), from, to);
        Float belowAverageCurrent = motorDataRepository.getCountOfDcCurrentBelowAverage(dcCurrentMin, organisation.getId(), from, to);
        telemetryThresholdDto.setDcCurrent(getPercentile(aboveAverageCurrent, belowAverageCurrent, countOfVehicle));
        Float aboveAverageMcsTemp = motorDataRepository.getCountOfMcsTempAboveAverage(mcsTempMax, organisation.getId(), from, to);
        Float belowAverageMcsTemp = motorDataRepository.getCountOfMcsTempBelowAverage(mcsTempMin, organisation.getId(), from, to);
        telemetryThresholdDto.setMcsTemp(getPercentile(aboveAverageMcsTemp, belowAverageMcsTemp, countOfVehicle));
        Float aboveAverageMotorTemp = motorDataRepository.getCountOfMotorTempAboveAverage(motorTempMax, organisation.getId(), from, to);
        Float belowAverageMotorTemp = motorDataRepository.getCountOfMotorTempBelowAverage(motorTempMin, organisation.getId(), from, to);
        telemetryThresholdDto.setMotorTemp(getPercentile(aboveAverageMotorTemp, belowAverageMotorTemp, countOfVehicle));
        Float aboveAverageBatteryTemp = telemetryBatteryRepository.getCountOfBatteryTempAboveAverage(batterySensorTempMax, organisation.getId(), from, to);
        Float belowAverageBatteryTemp = telemetryBatteryRepository.getCountOfBatteryTempBelowAverage(batterySensorTempMin, organisation.getId(), from, to);
        telemetryThresholdDto.setBatteryTemp(getPercentile(aboveAverageBatteryTemp, belowAverageBatteryTemp, countOfVehicle));
        return telemetryThresholdDto;
    }

    private MotorRangeData getPercentile(Float aboveAverage, Float belowAverage, Long countOfVehicle) {
        if (Optional.ofNullable(aboveAverage).isPresent()) {
            aboveAverage = (aboveAverage / countOfVehicle) * 100;
        }
        if (Optional.ofNullable(belowAverage).isPresent()) {
            belowAverage = (belowAverage / countOfVehicle) * 100;
        }
        return new MotorRangeData(Float.parseFloat(String.format("%.2f", belowAverage)), Float.parseFloat(String.format("%.2f", aboveAverage)));
    }

    private Optional<VehicleDto> entityToDto(Stream<Vehicle> vehicleStream) {

        return vehicleStream.map(e -> e)
                .map(e -> {
                    Map<String, Long> tests = new HashMap<>();

                    tests.put(TestTypeName.CONNECTIVITY.name().toLowerCase(),
                            vehicleTestRepository.findFirstByVehicleAndTestTypeAndStatusOrderByEndTimeDesc(e,
                                    testTypeRepository.findByTestTypeName(TestTypeName.CONNECTIVITY).get(), TestStatus.COMPLETED).map(x -> x.getId()).orElse(null));
                    tests.put(TestTypeName.DYNO.name().toLowerCase(),
                            vehicleTestRepository.findFirstByVehicleAndTestTypeAndStatusOrderByEndTimeDesc(e,
                                    testTypeRepository.findByTestTypeName(TestTypeName.DYNO).get(), TestStatus.COMPLETED).map(x -> x.getId()).orElse(null));
                    tests.put(TestTypeName.TEST_RIDE.name().toLowerCase(),
                            vehicleTestRepository.findLatestVehicleByTestTypeAndStatusOrderByEndTimeDesc(e.getId(),
                                    testTypeRepository.findByTestTypeName(TestTypeName.TEST_RIDE).get().getTestTypeName().name(), TestStatus.COMPLETED.name(), TestRideSummaryPopulationStatus.COMPLETED.name()).map(x -> x.getId()).orElse(null));
                    Long dateOfBirth = null;
                    Map<String,Long> onGoingTests= new HashMap<>();
                    onGoingTests.put(TestTypeName.CONNECTIVITY.name().toLowerCase(),
                            vehicleTestRepository.findFirstByVehicleAndTestTypeAndStatusOrderByEndTimeDesc(e,
                                    testTypeRepository.findByTestTypeName(TestTypeName.CONNECTIVITY).get() , TestStatus.RUNNING).map(x->x.getId()).orElse(null));
                    onGoingTests.put(TestTypeName.DYNO.name().toLowerCase(),
                            vehicleTestRepository.findFirstByVehicleAndTestTypeAndStatusOrderByEndTimeDesc(e,
                                    testTypeRepository.findByTestTypeName(TestTypeName.DYNO).get() , TestStatus.RUNNING).map(x->x.getId()).orElse(null));
                    onGoingTests.put(TestTypeName.TEST_RIDE.name().toLowerCase(),
                            vehicleTestRepository.findFirstByVehicleAndTestTypeAndStatusOrderByEndTimeDesc(e,
                                    testTypeRepository.findByTestTypeName(TestTypeName.TEST_RIDE).get() , TestStatus.RUNNING).map(x->x.getId()).orElse(null));

                    SubscriptionDetailsDto subscriptionDetailsDto = new SubscriptionDetailsDto();
                    activeVehicleSubscriptionPlanRepository.findByVehicle(e).ifPresent(plan -> {
                        ComboPlan comboPlan = plan.getComboPlan();
                        if(comboPlan!=null){
                            subscriptionDetailsDto.setComboPlanName(comboPlan.getName());
                        }
                        DataFrequencyPlan dataFrequencyPlan = plan.getDataFrequencyPlan();
                        if (dataFrequencyPlan != null) {
                            subscriptionDetailsDto.setDataFrequencyName(dataFrequencyPlan.getName());
                            subscriptionDetailsDto.setValue(dataFrequencyPlan.getValue());
                            subscriptionDetailsDto.setUnit((dataFrequencyPlan.getUnit()));
                        }
                    });

                    List<ImageDto> images = e.getVehicleModel().getColorImages().entrySet().stream()
                            .filter(v -> v.getKey().getId() == e.getColorModel().getId())
                            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue))
                            .values().stream()
                            .flatMap(Set::stream)
                            .map(value -> imageRepository.findById(value).orElseThrow(() -> new ImageException(evMessageBundle.getMessage("IMAGE_NOT_FOUND", value))))
                            .map(value -> new ImageDto(value.getTag(), value.getUrl()))
                            .collect(Collectors.toList());


                    return new VehicleDto(e.getId(), e.getImei(), e.getVehicleModel().getId()
                            , e.getVehicleModel().getName(), e.getMfrDate()
                            , dateOfBirth,
                            e.getVehicleParts().stream().map(x -> {
                                PartInfoDto partInfoDto = new PartInfoDto();
                                if (x.getPartModel().getPartType() == PartType.BATTERY) {
                                    BatteryModel bms = (BatteryModel) x.getPartModel();
                                    Set<PartModelAttribute> partModelAttributes = bms.getPartModelAttributes();
                                    partModelAttributes.forEach((attribute) -> {

                                        if (attribute.getName().equals("overVoltageThreshold")) {
                                            partInfoDto.setMax(Float.parseFloat(attribute.getValue()));
                                        } else if (attribute.getName().equals("underVoltageThreshold")) {
                                            partInfoDto.setMin(Float.parseFloat(attribute.getValue()));
                                        } else if (attribute.getName().equals("voltageUnit")) {
                                            partInfoDto.setUnit(attribute.getValue());
                                        }

                                    });
                                    partInfoDto.setId(bms.getId());
                                    partInfoDto.setName(bms.getName());
                                    return partInfoDto;
                                } else if (x.getPartModel().getPartType() == PartType.MCU) {
                                    McuModel motor = (McuModel) x.getPartModel();
                                    Set<PartModelAttribute> partModelAttributes = motor.getPartModelAttributes();
                                    partModelAttributes.forEach((attribute) -> {

                                        if (attribute.getName().equals("dcCurrentMax")) {
                                            partInfoDto.setMax(Float.parseFloat(attribute.getValue()));
                                        } else if (attribute.getName().equals("dcCurrentMin")) {
                                            partInfoDto.setMin(Float.parseFloat(attribute.getValue()));
                                        } else if (attribute.getName().equals("dcCurrentUnit")) {
                                            partInfoDto.setUnit(attribute.getValue());
                                        }

                                    });
                                    partInfoDto.setId(motor.getId());
                                    partInfoDto.setName(motor.getName());
                                    return partInfoDto;

                                } else {
                                    return new PartInfoDto(x.getId(), x.getPartModel().getName(), 0f, 0f, "na");
                                }

                            }).collect(Collectors.toList()),
                            images,
                            tests,
                            e.getVehicleModel().getDriveModes().stream().map(x -> {
                                return new DriveModeSpeedDto(x.getDriveMode().getName(), x.getMin(), x.getMax(), x.getUnit());
                            }).collect(Collectors.toList()),
                            Optional.ofNullable(e.getColorModel()).isPresent() ? colorModelService.findById(e.getColorModel().getId()).get().getName() : null,

                            Optional.ofNullable(e.getVehicleModel().getNetWeight()).orElse(0f), e.getTotalDistanceTraveled() != null ? (int) e.getTotalDistanceTraveled().floatValue() : null,
                            vehicleRegistrationDetailsRepository.findByVehicleId(e.getId())
                                    .map(VehicleRegistrationDetails::getRegistrationNumber)
                                    .orElse(null),
                            vehicleRegistrationDetailsRepository.findByVehicleId(e.getId())
                                    .map(details -> details.getSaleDate().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli())
                                    .orElse(null),
                            vehicleTestDurationRepository.findByTestTypeNameAndDataFrequencyPlan(TestTypeName.CONNECTIVITY, activeVehicleSubscriptionPlanRepository.findByVehicle(e).get().getDataFrequencyPlan()).get().getDuration()
                            ,
                            onGoingTests, subscriptionDetailsDto
                    );
                })
                .findAny();
    }

//  private Optional<VehicleErpInfo> dtoToEntity(Stream<VehicleErpDto> vehicleStream) {
//
//    return vehicleStream
//        .map(e ->
//            new VehicleErpInfo(e.getImeiNumber()
//                , vehicleModelRepository.findById(e.getVehicleModelCode())
//                .orElseThrow(() -> new VehicleModelException("Model Not found"))
//                , null
//                , e.getVin()
//
//                , e.getControllerNo(),
//                e.getIotDeviceCode(),
//                e.getChassisNumber()
//                , e.getEngineNo()
//
//                , vehicleColorService.findByColorCode(e.getColorCode()).get()
//                , e.getRegNo()
//                , e.getTempRegNo()
//                , Year.parse(e.getYear())
//                , LocalDate.parse(e.getMfrDate())
//            )
//        ).findAny();
//  }


    private Map<String, List<String>> generateData(String fieldName, Long minThreshold, Long maxThreshold) {
        Pageable sortByDesc =
                PageRequest.of(0, 10, Sort.by(fieldName).descending());
        List<String> maxImei = vehicleRepository.findByFieldNameAndPage(fieldName + ">" + maxThreshold, sortByDesc);
        Pageable sortByAsc =
                PageRequest.of(0, 10, Sort.by(fieldName).descending());
        List<String> minImei = vehicleRepository.findByFieldNameAndPage(fieldName + "<" + minThreshold, sortByDesc);
        return Map.of("maxImei", maxImei, "minImei", minImei);
    }

    private Float getDistanceTravelled(List<SpeedDto> speedDtoList, Instant odometerLastUpdatedAt, Float diameter) {
        Float distanceTravelled = 0f;
        Float factor = (float) (Math.PI * diameter * 60 / 1000);

        long startTime = odometerLastUpdatedAt.toEpochMilli();

        for (SpeedDto motorSpeedData : speedDtoList) {
            long timeDifference = Math.abs(motorSpeedData.getTimestamp().toEpochMilli() - startTime) / 1000;
            if (timeDifference <= 5) {
                distanceTravelled += motorSpeedData.getMotorSpeed() * factor * timeDifference / 3600;
            }
            startTime = motorSpeedData.getTimestamp().toEpochMilli();
        }
        return distanceTravelled;
    }

    @Override
    @Transactional
    public void updateVehicleStatus(Long orgId, Boolean status) {
        OperationStatus vehicleOperationStatus;
        OrganisationImpl<CustomUser> organisation = customOrganisationRepository.findById(orgId).orElseThrow(() -> new OrganisationNotFoundException("organisation not found"));
        CustomOrganisation customOrganisation = (CustomOrganisation) organisation;
        List<Vehicle> vehicles = vehicleRepository.findByManufacturerOrDealershipOrOwner(customOrganisation, customOrganisation, customOrganisation);
        for (Vehicle v : vehicles) {
            if (status) {
                vehicleOperationStatus = OperationStatus.ACTIVE;
            } else {
                vehicleOperationStatus = OperationStatus.INACTIVE;
            }
            v.setOperationStatus(vehicleOperationStatus);
        }
        vehicleRepository.saveAll(vehicles);
    }


    @Override
    public List<DriveModeAndRangeDto> getDriveModesAndRangesByImei(String identifier) {
        List<DriveModeAndRangeDto> driveModesAndRanges = new ArrayList<>();
        Vehicle vehicle = getVehicleByAnyId(identifier);

        driveModesAndRanges = vehicle.getVehicleModel().getDriveModes().stream().map(e ->
                new DriveModeAndRangeDto(e.getDriveMode().getName(), e.getRange())
        ).collect(Collectors.toList());

        return driveModesAndRanges;
    }

    public Vehicle getVehicleByAnyId(String identifier) {
        return getVehicle(identifier)
                .orElseThrow(() -> new VehicleException(evMessageBundle.getMessage("VEHICLE_NOT_FOUND")));
    }

    @Override
    public VehiclePartAttributeDetailsDto getPartAttributesByImei(String identifier, PartType partType, String attributeName) {

        VehiclePartAttributeDetailsDto vehiclePartAttributeDetails = new VehiclePartAttributeDetailsDto();
        Map<String, String> attributes = new HashMap<>();
        Long modelId;
        Vehicle vehicle = getVehicleByAnyId(identifier);

        if (Optional.ofNullable(attributeName).isPresent()) {
            PartModelAttributeDto partModelAttributeProjection = Optional.ofNullable(vehicleCacheRetrievalService.getAttribute(attributeName, partType.name(), vehicle)).orElseThrow(() -> new PartModelException("part model attribute " + attributeName + " not found"));
            attributes.put(partModelAttributeProjection.getName(), partModelAttributeProjection.getValue());
            modelId = partModelAttributeProjection.getModelId();
        } else {
            AtomicReference<Long> id = new AtomicReference<>();
            vehicleCacheRetrievalService.getPartAttributes(partType.name(), vehicle).stream().forEach(e -> {
                attributes.put(e.getName(), e.getValue());
                if (id.get() == null) {
                    id.set(e.getModelId());
                }
            });
            modelId = id.get();
        }
        if (Optional.ofNullable(modelId).isEmpty()) {
            modelId = partModelRepository.getPartModelIdForVehiclePart(partType.name(), vehicle.getId()).orElseThrow(() -> new PartModelException("part model with part type '" + partType + "' not found"));
        }

        partModelRepository.findById(modelId).ifPresent(e -> {
            vehiclePartAttributeDetails.setModelName(e.getName());
            vehiclePartAttributeDetails.setManufacturerName(e.getManufacturer().getOrganisationProfile().getName());
        });

        vehiclePartAttributeDetails.setAttributes(attributes);

        return vehiclePartAttributeDetails;
    }

    @Override
    @Transactional
    public VehicleLatestData saveVehicleLatitudeAndLongitude(String imei, VehicleLatestData vehicleLatestData) {
        Instant start = Instant.now().minusSeconds(secondsInWeek);
        if (Optional.ofNullable(vehicleLatestData.getLocationUpdatedAt()).isPresent() && start.isBefore(vehicleLatestData.getLocationUpdatedAt())) {
            start = vehicleLatestData.getLocationUpdatedAt();
        }
        VehicleLocationData availableVehicleLocationData = locationDataRepository.findFirstByTelemetryIdxImeiAndTelemetryIdxTimestampBetweenOrderByTelemetryIdxTimestampDesc(imei, start, Instant.now());

        if (Optional.ofNullable(availableVehicleLocationData).isPresent()) {
            Vehicle vehicle = availableVehicleLocationData.getVehicle();
            if (vehicle.getLocationUpdatedAt() == null || availableVehicleLocationData.getTelemetryIdx().getTimestamp().isAfter(vehicle.getLocationUpdatedAt())) {
                vehicle.setLocationUpdatedAt(availableVehicleLocationData.getTelemetryIdx().getTimestamp());
                vehicle.setLatitude(availableVehicleLocationData.getLatitude());
                vehicle.setLongitude(availableVehicleLocationData.getLongitude());
                vehicleRepository.save(vehicle);
                log.info("Updated Lat And Long of IMEI NO. {} latitude : {}, longitude :{}", imei, availableVehicleLocationData.getLatitude(), availableVehicleLocationData.getLongitude());
            }
            if (vehicleLatestData.getLocationUpdatedAt() == null || availableVehicleLocationData.getTelemetryIdx().getTimestamp().isAfter(vehicleLatestData.getLocationUpdatedAt())) {
                vehicleLatestData.setLocationUpdatedAt(availableVehicleLocationData.getTelemetryIdx().getTimestamp());
                vehicleLatestData.setLatitude(availableVehicleLocationData.getLatitude());
                vehicleLatestData.setLongitude(availableVehicleLocationData.getLongitude());
            }
        }

        return vehicleLatestData;
    }

    @Override
    @Transactional
    public VehicleLatestData getRecentVehicleBatteryData(String imei, VehicleLatestData vehicleLatestData) {
        Instant start = Instant.now().minusSeconds(secondsInWeek);
        if (Optional.ofNullable(vehicleLatestData.getSocUpdatedAt()).isPresent() && start.isBefore(vehicleLatestData.getSocUpdatedAt())) {
            start = vehicleLatestData.getSocUpdatedAt();
        }
        Optional<VehicleBatteryData> vehicleBatteryData = telemetryBatteryRepository.findFirstByTelemetryIdxImeiAndTelemetryIdxTimestampBetweenAndSocIsNotNullOrderByTelemetryIdxTimestampDesc(imei, start, Instant.now());
        if (vehicleBatteryData.isPresent() && (vehicleLatestData.getSoc() == null || vehicleBatteryData.get().getTelemetryIdx().getTimestamp().isAfter(vehicleLatestData.getSocUpdatedAt()))) {
            vehicleLatestData.setSocUpdatedAt(vehicleBatteryData.get().getTelemetryIdx().getTimestamp());
            vehicleLatestData.setSoc(vehicleBatteryData.get().getSoc());
            log.info("Updated SOC of IMEI NO. {} soc : {}", imei, vehicleBatteryData.get().getSoc());
        }
        return vehicleLatestData;
    }


    /**
     * This Method updates the vehicle_state in vehicle table and adds an entry in vehicle_status table specifying the current state of the vehicle
     *
     * @param imei        - imei of the vehicle
     * @param currentTime - current timestamp
     */
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public VehicleEvent saveVehicleState(String imei, Instant currentTime, DataFrequencyPlanDetails dataFrequencyPlanDetails) {
        VehicleState vehicleState = null;
        try {
            Vehicle vehicle = vehicleRepository.findByImei(imei).orElseThrow(() -> new VehicleException(evMessageBundle.getMessage("VEHICLE_NOT_FOUND")));
            VehicleStatusProjection vehicleStatusProjection = getVehicleStatusAggregateData(imei, currentTime, dataFrequencyPlanDetails);
            vehicleState = findVehicleState(vehicleStatusProjection);
            log.info("vehicle {} is in state {} at {}", imei, vehicleState, currentTime);
            //updating  the vehicle state
            vehicle.setVehicleState(vehicleState);
            vehicleRepository.save(vehicle);
            //getting the latest location details and saving the entry in vehicle_status
            List<RunningMetricsDataProjection> runningMetricsDataProjections = this.getRunningMetricsData(imei, currentTime.minusSeconds(10), currentTime);
            log.info("VEHICLE_STATUS Fetched running metrics data for imei : {} between {} and {}", imei, currentTime.minusSeconds(10), currentTime);
            RunningMetricsDataProjection latestLocation = !runningMetricsDataProjections.isEmpty() ? runningMetricsDataProjections.get(runningMetricsDataProjections.size() - 1) : null;
            VehicleStatus vehicleStatus = new VehicleStatus(new VehicleStatusIdx(currentTime, imei), vehicleState, latestLocation != null ? latestLocation.getLatitude() : null, latestLocation != null ? latestLocation.getLongitude() : null, UpdateSource.VEHICLE_STATUS_CRON);
            vehicleStatus.setVehicle(vehicle);
            vehicleStatusRepository.save(vehicleStatus);
        } catch (VehicleException e) {
            log.error("Error updating vehicle status ",e);
        }
        return new VehicleEvent(imei, currentTime, dataFrequencyPlanDetails, vehicleState);

    }

    /**
     * This method will check if there was any data delay in the hour before the previous hour, if it finds any data delay it will update the vehicle_status accordingly for that entire hour
     *
     * @param imei                     imei of a vehicle
     * @param currentTime              CRON time
     * @param dataFrequencyPlanDetails dataFrequencyPlanDetails of a vehicle
     */
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void updateVehicleStateForDataDelay(String imei, Instant currentTime, DataFrequencyPlanDetails dataFrequencyPlanDetails) {
        log.debug("updating the data delay status for imei {} at time {}", imei, currentTime);
        List<VehicleStatus> updatedVehicleStatus = new ArrayList<>();
        Instant startTime = currentTime.minusSeconds(3660);
        List<Instant> dataDelayedTimestamps = getDataDelayedTimestamps(imei, startTime, currentTime, dataDelayInterval, dataFrequencyPlanDetails);
        List<VehicleStatus> vehicleStatuses = vehicleStatusRepository.findByVehicleStatusIdxImeiAndVehicleStatusIdxTimestampIn(imei, dataDelayedTimestamps);

        vehicleStatuses.forEach(vehicleStatus -> {
            VehicleStatusProjection vehicleStatusProjection = getVehicleStatusAggregateData(imei, vehicleStatus.getVehicleStatusIdx().getTimestamp(), dataFrequencyPlanDetails);
            VehicleState updatedVehicleState = findVehicleState(vehicleStatusProjection);
            log.debug("updatedVehicleState {}, imei {}, time {}", updatedVehicleState, imei, vehicleStatus.getVehicleStatusIdx().getTimestamp());
            vehicleStatus.setVehicleState(updatedVehicleState);
            vehicleStatus.setUpdateSource(UpdateSource.VEHICLE_STATUS_DATA_DELAY_CRON);
            updatedVehicleStatus.add(vehicleStatus);
        });

        log.debug("size of updatedVehicleStatus records {} for vehicle {} at time {}", updatedVehicleStatus.size(), imei, currentTime);
        vehicleStatusRepository.saveAll(updatedVehicleStatus);
    }

    private List<Instant> getDataDelayedTimestamps(String imei, Instant startTime, Instant endTime, String dataDelayInterval, DataFrequencyPlanDetails dataFrequencyPlanDetails) {
        List<Instant> timestamps = new ArrayList<>();
        Set<AggregateName> aggregateNames = dataFrequencyPlanDetails.getAggregateName();
        java.util.function.Predicate<AggregateName> isTelemetryAggregate = aggregateName -> aggregateName.getType().equals(EntityType.TELEMETRY);
        java.util.function.Predicate<AggregateName> isBatteryAggregate = aggregateName -> aggregateName.getType().equals(EntityType.BATTERY);

        Optional<AggregateName> telemetryAggregate = aggregateNames.stream().filter(isTelemetryAggregate).findFirst();
        Optional<AggregateName> batteryAggregate = aggregateNames.stream().filter(isBatteryAggregate).findFirst();

        if (telemetryAggregate.isEmpty() || batteryAggregate.isEmpty()) {
            throw new AggregateNotFoundException(evMessageBundle.getMessage("AGGREGATE_NOT_FOUND"));

        }
        String queryString = "select time_bucket " +
                "from evdata." + telemetryAggregate.get().getName() + " vsta " +
                "where vsta.imei = :imei " +
                "and vsta.created_on between :startTime and :endTime " +
                "and vsta.created_on - vsta.time_bucket > CAST(:interval AS INTERVAL) " +
                "union " +
                "select time_bucket " +
                "from evdata." + batteryAggregate.get().getName() + " vsba " +
                "where vsba.imei = :imei " +
                "and vsba.created_on between :startTime and :endTime " +
                "and vsba.created_on - vsba.time_bucket > CAST(:interval AS INTERVAL) ";
        Query query = entityManager.createNativeQuery(queryString);
        query.setParameter("imei", imei);
        query.setParameter("startTime", startTime);
        query.setParameter("endTime", endTime);
        query.setParameter("interval", dataDelayInterval);
        List<Object> resultList = query.getResultList();
        for (Object result : resultList) {
            Instant timeBucket = (Instant) result;
            timestamps.add(timeBucket);
        }
        return timestamps;
    }

    /**
     * This method updates the vehicle's runningTime,chargingTime and stoppageTime in vehicle_event_monitor table based on current vehicle_state
     *
     * @param vehicle       vehicle
     * @param vehicleStatus current vehicleStatus
     */
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public VehicleStateEvent updateVehicleRunningEvent(Vehicle vehicle, VehicleStatus vehicleStatus) {
        if (vehicle == null || vehicleStatus == null || vehicleStatus.getVehicleState() == null) {
            log.error("Vehicle or VehicleStatus is null, cannot update running event");
            return null;
        }
        Long timeDifference;
        Long runningTime = 0L;
        Long stoppageTime = 0L;
        Long chargingTime = 0L;
        VehicleEventMonitor vehicleEventMonitor = null;
        try {
            Optional<VehicleEventMonitor> vehicleEventMonitorOptional = vehicleEventMonitorRepository.findByVehicle(vehicle);
            vehicleEventMonitor = vehicleEventMonitorOptional.orElseGet(() -> new VehicleEventMonitor(vehicle.getImei(), vehicle, 0L, null, 0L, null));
            if (VehicleState.RUNNING.matches(vehicleStatus.getVehicleState().name())) {
                Instant runningTimeLastUpdated = vehicleEventMonitor.getRunningTimeLastUpdatedOn();
                timeDifference = runningTimeLastUpdated != null ? Duration.between(runningTimeLastUpdated, vehicleStatus.getVehicleStatusIdx().getTimestamp()).toSeconds() : 0;
                runningTime = vehicleEventMonitor.getRunningTime() + timeDifference;
                vehicleEventMonitor.setRunningTimeLastUpdatedOn(vehicleStatus.getVehicleStatusIdx().getTimestamp());
                vehicleEventMonitor.setRunningTime(runningTime);
                vehicleEventMonitor.setStoppageTime(0L);
                vehicleEventMonitor.setStoppageTimeLastUpdatedOn(null);
                vehicleEventMonitor.setChargingTime(0L);
                vehicleEventMonitor.setChargingTimeLastUpdatedOn(null);
            } else  if (VehicleState.CHARGING.matches(vehicleStatus.getVehicleState().name())) {
                Instant chargingTimeLastUpdated = vehicleEventMonitor.getChargingTimeLastUpdatedOn();
                timeDifference = chargingTimeLastUpdated != null ? Duration.between(chargingTimeLastUpdated, vehicleStatus.getVehicleStatusIdx().getTimestamp()).toSeconds() : 0;
                chargingTime = vehicleEventMonitor.getChargingTime() + timeDifference;
                vehicleEventMonitor.setChargingTimeLastUpdatedOn(vehicleStatus.getVehicleStatusIdx().getTimestamp());
                vehicleEventMonitor.setChargingTime(chargingTime);
                vehicleEventMonitor.setStoppageTime(0L);
                vehicleEventMonitor.setStoppageTimeLastUpdatedOn(null);
                vehicleEventMonitor.setRunningTime(0L);
                vehicleEventMonitor.setRunningTimeLastUpdatedOn(null);
            } else {
                Instant stoppageTimeLastUpdated = vehicleEventMonitor.getStoppageTimeLastUpdatedOn();
                timeDifference = stoppageTimeLastUpdated != null ? Duration.between(stoppageTimeLastUpdated, vehicleStatus.getVehicleStatusIdx().getTimestamp()).toSeconds() : 0;
                stoppageTime = vehicleEventMonitor.getStoppageTime() + timeDifference;
                vehicleEventMonitor.setStoppageTimeLastUpdatedOn(vehicleStatus.getVehicleStatusIdx().getTimestamp());
                vehicleEventMonitor.setStoppageTime(stoppageTime);
                vehicleEventMonitor.setRunningTime(0L);
                vehicleEventMonitor.setRunningTimeLastUpdatedOn(null);
                vehicleEventMonitor.setChargingTime(0L);
                vehicleEventMonitor.setChargingTimeLastUpdatedOn(null);
            }
            vehicleEventMonitorRepository.save(vehicleEventMonitor);

        }catch (Exception e){
            log.error("Error updating vehicle running event for vehicle {}: {}", vehicle.getImei(), e.getMessage());
        }
        return new VehicleStateEvent(vehicleEventMonitor,vehicleStatus);
    }



    /**
     * This method gives the data from the aggregates based on the data frequency plan for the vehicle
     *
     * @param dataFrequencyPlanDetails data_frequency_plan_details for the vehicle
     * @return return the data required to identify the vehicle_state
     */
    public VehicleStatusProjection getVehicleStatusAggregateData(String imei, Instant currentTime, DataFrequencyPlanDetails dataFrequencyPlanDetails) {
        Set<AggregateName> aggregateNames = dataFrequencyPlanDetails.getAggregateName();

        java.util.function.Predicate<AggregateName> isTelemetryAggregate = aggregateName -> aggregateName.getType().equals(EntityType.TELEMETRY);
        java.util.function.Predicate<AggregateName> isBatteryAggregate = aggregateName -> aggregateName.getType().equals(EntityType.BATTERY);
        java.util.function.Predicate<AggregateName> isMotorAggregate = aggregateName -> aggregateName.getType().equals(EntityType.MOTOR);

        Optional<AggregateName> telemetryAggregate = aggregateNames.stream().filter(isTelemetryAggregate).findFirst();
        Optional<AggregateName> batteryAggregate = aggregateNames.stream().filter(isBatteryAggregate).findFirst();
        Optional<AggregateName> motorAggregate = aggregateNames.stream().filter(isMotorAggregate).findFirst();

        if (telemetryAggregate.isEmpty() || batteryAggregate.isEmpty()|| motorAggregate.isEmpty()) {
            throw new AggregateNotFoundException(evMessageBundle.getMessage("AGGREGATE_NOT_FOUND"));
        }

        VehicleStatusProjection vehicleStatusProjection = null;
        Instant vehicleStatusAt = getTimeBasedOnComputationFrequency(dataFrequencyPlanDetails.getComputationFrequency(), currentTime);

        String queryString = "WITH sorted_vsta AS ( " +
                "    SELECT imei, di_main_power, di_ignition, di_motion, time_bucket " +
                "    FROM " + telemetryAggregate.get().getName() + " " +
                "    WHERE imei = :imei " +
                "    AND time_bucket BETWEEN :startTime AND :endTime " +
                "    ORDER BY time_bucket ASC " +
                "), " +
                "sorted_vsba AS ( " +
                "    SELECT imei, current_sign, time_bucket " +
                "    FROM " + batteryAggregate.get().getName() + " " +
                "    WHERE imei = :imei " +
                "    AND time_bucket BETWEEN :startTime AND :endTime " +
                "    ORDER BY time_bucket ASC " +
                ") ," +
                "sorted_vsma AS ( " +
                "    SELECT imei, motor_speed, time_bucket " +
                "    FROM " + motorAggregate.get().getName() + " " +
                "    WHERE imei = :imei " +
                "    AND time_bucket BETWEEN :startTime AND :endTime " +
                "    ORDER BY time_bucket ASC " +
                ") " +
                "SELECT COALESCE(vsta.imei, vsba.imei,vsma.imei) AS imei, " +
                "       vsta.di_main_power AS diMainPower, " +
                "       vsta.di_ignition AS diIgnition, " +
                "       vsta.di_motion AS diMotion, " +
                "       vsba.current_sign AS currentSign, " +
                "       vsma.motor_speed AS motorSpeed " +
                "FROM sorted_vsta vsta " +
                "FULL OUTER JOIN sorted_vsba vsba " +
                "ON vsta.imei = vsba.imei " +
                "AND vsta.time_bucket = vsba.time_bucket " +
                "FULL OUTER JOIN sorted_vsma vsma " +
                "ON vsma.time_bucket = vsba.time_bucket " +
                "AND vsma.imei = vsba.imei " +
                "ORDER BY vsta.time_bucket ASC " +
                "LIMIT 1";
        Query query = entityManager.createNativeQuery(queryString);
        query.setParameter("imei", imei);
        query.setParameter("startTime", vehicleStatusAt);
        query.setParameter("endTime", currentTime);


        List<Object[]> resultList = query.getResultList();
        if (!resultList.isEmpty()) {
            try{
                Object[] row = resultList.get(0);
                String resImei = row[0].toString();
                Boolean diMainPower = (Boolean) row[1];
                Boolean diIgnition = (Boolean) row[2];
                Boolean diMotion = (Boolean) row[3];
                Integer currentSign = (Integer) row[4];
                Float motorSpeed = (Float) row[5];
                vehicleStatusProjection = new VehicleStatusProjection(resImei, diMainPower, diIgnition, diMotion, currentSign,motorSpeed);
            }catch(Exception e){
                log.error("Error in status determination ",e);
            }
        }

        return vehicleStatusProjection;
    }

    /**
     * This method gives the time for vehicleStatus based on computationFrequency to get the 3rd recent entry from the aggregates
     */
    Instant getTimeBasedOnComputationFrequency(Short computationFrequency, Instant currentTime) {
        int secondsToSubtract = computationFrequency * 2;
        return currentTime.minusSeconds(secondsToSubtract);
    }


    @Override
    public VehicleStatusListDto getVehicleByStatus(Optional<Long> fleetId, Optional<String> status, Long organisationId, Pageable pageable) {
        CustomOrganisation customOrganisation = (CustomOrganisation) customOrganisationRepository.findById(organisationId).orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("ORG_NOT_FOUND")));
        log.debug("organisation {}", customOrganisation.getId());
        VehicleStatusListDto vehicleStatusListDto = new VehicleStatusListDto();
        vehicleStatusListDto.setStatusMap(new HashMap<>());
        fleetId.ifPresentOrElse(
                id -> {
                    Fleet fleet = Optional.ofNullable(fleetRepository.findByIdAndOrganisation(fleetId.get(), customOrganisation))
                            .orElseThrow(() -> new FleetException(evMessageBundle.getMessage("FLEET_NOT_FOUND_IN_ORGANISATION", fleetId.get())));
                    status.ifPresentOrElse(
                            state -> {
                                VehicleState vehicleState = VehicleState.valueOf(state.toUpperCase());
                                Page<VehicleIdsProjection> vehiclePage = vehicleRepository.getFleetVehiclesByStatus(customOrganisation.getId(), state.toUpperCase(), fleet.getId(), pageable);
                                log.debug("vehicleState {}", vehicleState);
                                vehicleStatusListDto.getStatusMap().put(vehicleState, populateVehicleListDataDto(vehiclePage));
                            },
                            () -> {
                                for (VehicleState vehicleState : VehicleState.values()) {
                                    log.debug("vehicleState {}", vehicleState);
                                    Page<VehicleIdsProjection> vehiclePage = vehicleRepository.getFleetVehiclesByStatus(customOrganisation.getId(), vehicleState.name(), fleet.getId(), pageable);
                                    vehicleStatusListDto.getStatusMap().put(vehicleState, populateVehicleListDataDto(vehiclePage));
                                }
                            }
                    );
                },
                () -> {
                    status.ifPresentOrElse(
                            state -> {
                                VehicleState vehicleState = VehicleState.valueOf(state.toUpperCase());
                                Page<VehicleIdsProjection> vehiclePage = vehicleRepository.getVehiclesByStatus(customOrganisation.getId(), state.toUpperCase(), pageable);
                                log.debug("vehicleState {}", vehicleState);
                                vehicleStatusListDto.getStatusMap().put(vehicleState, populateVehicleListDataDto(vehiclePage));
                            },
                            () -> {
                                for (VehicleState vehicleState : VehicleState.values()) {
                                    Page<VehicleIdsProjection> vehiclePage = vehicleRepository.getVehiclesByStatus(customOrganisation.getId(), vehicleState.name(), pageable);
                                    log.debug("vehicleState {}", vehicleState);
                                    vehicleStatusListDto.getStatusMap().put(vehicleState, populateVehicleListDataDto(vehiclePage));
                                }
                            }
                    );
                }
        );
        return vehicleStatusListDto;
    }

    @Override
    public VehicleStatusListAllDto getVehiclesByVehicleStateWithOutPagination(Optional<Long> fleetId, Optional<VehicleState> status, Long organisationId) {
        VehicleStatusListAllDto vehicleStatusListAllDto = new VehicleStatusListAllDto();
        Map<VehicleState, List<VehicleLocationDto>> vehicles = new HashMap<>();
        VehicleStatusListDto vehicleStatusListDto;
        if (status.isPresent()) {
            vehicleStatusListDto = getVehicleByStatus(fleetId, Optional.of(status.get().name()), organisationId, null);
        } else {
            Pageable pageable = PageRequest.of(0, 300, Sort.by("imei").ascending());
            vehicleStatusListDto = getVehicleByStatus(fleetId, Optional.empty(), organisationId, pageable);
        }

        Set<VehicleState> vehicleStates = vehicleStatusListDto.getStatusMap().keySet();

        for (VehicleState vehicleState : vehicleStates) {
            List<VehicleLocationDto> vehicleLocationDtoList = new ArrayList<>();
            VehicleListDataDto vehicleListDataDto = vehicleStatusListDto.getStatusMap().get(vehicleState);
            List<String> imeis = vehicleListDataDto.getImei();
            vehicleLocationDtoList = vehicleRepository.findLocationByImeiIn(imeis).stream().map(e -> new VehicleLocationDto(e.getImei(), e.getLatitude(), e.getLongitude())).toList();
            vehicles.put(vehicleState, vehicleLocationDtoList);
        }

        vehicleStatusListAllDto.setVehicles(vehicles);
        return vehicleStatusListAllDto;
    }

    private VehicleListDataDto populateVehicleListDataDto(Page<VehicleIdsProjection> vehiclePage) {
        VehicleListDataDto vehicleListDataDto = new VehicleListDataDto();
        List<String> imeiList = vehiclePage.stream().map(VehicleIdsProjection::getVehImei).toList();
        vehicleListDataDto.setImei(imeiList);
        vehicleListDataDto.setVehicles(vehiclePage.getContent());
        vehicleListDataDto.setTotalPages(vehiclePage.getTotalPages());
        vehicleListDataDto.setTotalElements(vehiclePage.getTotalElements());
        return vehicleListDataDto;
    }


    @Override
    public VehicleStatusCountDto getVehicleCount(Optional<Long> fleetId, Long organisationId) {
        CustomOrganisation customOrganisation = (CustomOrganisation) customOrganisationRepository.findById(organisationId).orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("ORG_NOT_FOUND")));

        try {
            userOrganisationUtils.isFleetManager();
            String sub = SecurityContextHolder.getContext().getAuthentication().getName();
            CustomUser user = userRepository.findByEmailIgnoreCase(sub).orElseThrow(() -> new UserProfileNotFoundException("User does not exists"));
            List<Fleet> fleets = fleetRepository.findByUsers(user.getId()).stream().filter(fleet -> fleet.getOrganisation().getId().equals(organisationId)).toList();
            fleetId = Optional.of(fleets.get(0).getId());
        } catch (Exception e) {
            log.info("user is not a fleet manager");
        }
        log.debug("organisation {}", customOrganisation.getId());
        VehicleStatusCountDto vehicleStatusCountDto = new VehicleStatusCountDto();
        AtomicReference<List<VehicleStateCountProjection>> vehicleStateCounts = new AtomicReference<>();
        Optional<Long> finalFleetId = fleetId;
        fleetId.ifPresentOrElse(
                id -> {
                    Fleet fleet = Optional.ofNullable(fleetRepository.findByIdAndOrganisation(finalFleetId.get(), customOrganisation)).orElseThrow(() -> new FleetException(evMessageBundle.getMessage("FLEET_NOT_FOUND_IN_ORGANISATION", finalFleetId.get())));
                    vehicleStateCounts.set(vehicleRepository.getStateCountForFleet(customOrganisation.getId(), fleet.getId()));
                    log.debug("inside fleet, size : {}", vehicleStateCounts.get().size());
                },
                () -> {
                    vehicleStateCounts.set(vehicleRepository.getStateCount(customOrganisation.getId()));
                    log.debug("inside org, size : {}", vehicleStateCounts.get().size());
                }
        );
        VehicleStatusCountDto resultCountDto = mapVehicleStateCountsToDto(vehicleStateCounts.get(), vehicleStatusCountDto);
        return resultCountDto;
    }

    public VehicleStatusCountDto mapVehicleStateCountsToDto(List<VehicleStateCountProjection> vehicleStateCounts, VehicleStatusCountDto vehicleStatusCountDto) {
        for (VehicleStateCountProjection vehicleStateCountProjection : vehicleStateCounts) {
            String vehicleState = vehicleStateCountProjection.getVehicleState();
            int stateCount = vehicleStateCountProjection.getStateCount();
            if (VehicleState.OFFLINE.matches(vehicleState)) {
                vehicleStatusCountDto.setOffline(stateCount);
            } else if (VehicleState.TOWING.matches(vehicleState)) {
                vehicleStatusCountDto.setTowing(stateCount);
            } else if (VehicleState.RUNNING.matches(vehicleState)) {
                vehicleStatusCountDto.setRunning(stateCount);
            } else if (VehicleState.CHARGING.matches(vehicleState)) {
                vehicleStatusCountDto.setCharging(stateCount);
            } else if (VehicleState.IDLING.matches(vehicleState)) {
                vehicleStatusCountDto.setIdling(stateCount);
            } else if (VehicleState.STOPPED.matches(vehicleState)) {
                vehicleStatusCountDto.setStopped(stateCount);
            }
        }
        return vehicleStatusCountDto;
    }


    @Override
    public String getVehicleState(String identifier, Long organisationId) {
        CustomOrganisation customOrganisation = (CustomOrganisation) customOrganisationRepository.findById(organisationId).orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("ORG_NOT_FOUND")));
        log.debug("organisation {}", customOrganisation.getId());
        Vehicle vehicle = getVehicle(identifier).orElseThrow(() -> new VehicleException(evMessageBundle.getMessage("VEHICLE_NOT_FOUND")));
        if (!Objects.equals(vehicle.getManufacturer().getId(), organisationId)
                || vehicle.getOperationStatus() != OperationStatus.ACTIVE) {
            throw new VehicleException(evMessageBundle.getMessage("VEHICLE_NOT_FOUND"));
        }
        return vehicle.getVehicleState().name();
    }

    /**
     * This Method is used to find the vehicle_state by taking the input parameters
     *
     * @param vehicleStatusProjection - input parameters includes imei, time_bucket, di_main_power, di_ignition, di_motion, current_sign
     * @return this method returns the vehicle_state based on the input parameters
     */
    public VehicleState findVehicleState(VehicleStatusProjection vehicleStatusProjection) {
        VehicleState vehicleState;
        if (vehicleStatusProjection == null || (vehicleStatusProjection.getDiMainPower() != null && !vehicleStatusProjection.getDiMainPower())) {
            vehicleState = VehicleState.OFFLINE;
        }
        else{
            vehicleState = VehicleState.STOPPED;
            if (vehicleStatusProjection.getMotorSpeed() != null && vehicleStatusProjection.getMotorSpeed() >= 0) {
                vehicleState = VehicleState.RUNNING;
            } else if (vehicleStatusProjection.getDiIgnition() != null && vehicleStatusProjection.getDiMotion() != null && !vehicleStatusProjection.getDiIgnition() && vehicleStatusProjection.getDiMotion()) {
                vehicleState = VehicleState.TOWING;
            }  else if (vehicleStatusProjection.getDiIgnition() != null && vehicleStatusProjection.getDiMotion() != null && vehicleStatusProjection.getCurrentSign() != null &&
                    vehicleStatusProjection.getDiIgnition() && !vehicleStatusProjection.getDiMotion() && vehicleStatusProjection.getCurrentSign() <= 0) {
                vehicleState = VehicleState.IDLING;
            } else if (vehicleStatusProjection.getDiMotion() != null && vehicleStatusProjection.getCurrentSign() != null && !vehicleStatusProjection.getDiMotion() && vehicleStatusProjection.getCurrentSign() > 0) {
                vehicleState = VehicleState.CHARGING;
            }
            // Skipped !vehicleStatusProjection.getDiMotion() check for STOPPED as it is the default state if data comes in
        }
        return vehicleState;
    }

    @Override
    public PartModelTreeDto getPartModelTreeView(String imei) {

        Vehicle vehicle = getVehicleByAnyId(imei);

        //This basic version will work for 1 level i.e. vehicle model-part model association. To be improved after part model-part model association is done
        List<VehiclePartModelProjection> vehiclePartModelProjectionList = vehicleRepository.getVehiclePartModels(vehicle.getId());
        PartModel partModel = partModelRepository.findById(vehicle.getVehicleModel().getId()).orElseThrow(() -> new PartModelException(evMessageBundle.getMessage("PART_MODEL_ID_NOT_FOUND", vehicle.getVehicleModel().getId())));
        Map<PartType, List<PartModelTreeDto>> partModelTrees = vehiclePartModelProjectionList.stream()
                .collect(Collectors.toMap(
                        VehiclePartModelProjection::getPartType,
                        e -> List.of(new PartModelTreeDto(
                                e.getId(), e.getPartType(), e.getName(), new HashMap<>()
                        ))
                ));

        return new PartModelTreeDto(partModel.getId(), partModel.getPartType(), partModel.getName(), partModelTrees);
    }


    public VehicleDistanceDto getVehicleDistanceBetween(String identifier, Long from, Long to, TimeFilter timeFilter) {
        DecimalFormat decimalFormatter = new DecimalFormat("#.##");
        VehicleDistanceDto vehicleDistanceDto = new VehicleDistanceDto();
        Instant startTime = Instant.ofEpochMilli(from);
        Instant endTime = Instant.ofEpochMilli(to);
        Vehicle vehicle = getVehicleByAnyId(identifier);
        String imei = vehicle.getImei();
        Float totalVehicleDistance = vehicleRunningMetricsRepository.getTotalDistanceTravelledBetweenTimeAndImei(startTime, endTime, imei);
        vehicleDistanceDto.setImei(imei);
        vehicleDistanceDto.setOdometerReading(Float.valueOf(decimalFormatter.format(Optional.ofNullable(vehicle.getTotalDistanceTraveled()).orElse(0.0f))));
        vehicleDistanceDto.setDistanceTravelled(Float.valueOf(decimalFormatter.format(Optional.ofNullable(totalVehicleDistance).orElse(0.0f))));
        vehicleDistanceDto.setTimeDistanceBreakUps(getTimeBreakUpsDistance(imei, from, to, timeFilter));
        return vehicleDistanceDto;
    }

    /**
     * This Method will return the list of time and distance break ups for a vehicle in given period of time and given timeFliter
     *
     * @param imei       - vehicle imei
     * @param from       - lower limit of the time interval
     * @param to         - upper limit of the time interval
     * @param timeFilter - it can be DAILY/WEEKLY/MONTHLY, based on this the break up of the data takes place
     * @return this method return the time breakups based on the timeFilter and distance travelled by the vehicle between each time breakUp
     */
    private List<TimeIntervalsDto> getTimeBreakUpsDistance(String imei, Long from, Long to, TimeFilter timeFilter) {
        DecimalFormat decimalFormatter = new DecimalFormat("#.##");
        Integer breakUpInterval = 3600 * 24; // for 1-day time interval
        if (timeFilter.equals(TimeFilter.DAY)) {
            breakUpInterval = 3600; // for 1-hour time interval
        }
        Instant startTime = Instant.ofEpochMilli(from);
        Instant endTime = Instant.ofEpochMilli(to);
        List<TimeIntervalsDto> timeIntervals = new ArrayList<>();
        while (startTime.isBefore(endTime)) {
            Instant intervalEndTime = startTime.plusSeconds(breakUpInterval);
            if (intervalEndTime.isAfter(endTime)) {
                intervalEndTime = endTime;
            }
            timeIntervals.add(new TimeIntervalsDto(startTime.toEpochMilli(), intervalEndTime.toEpochMilli(), Float.valueOf(decimalFormatter.format(vehicleRunningMetricsRepository.getTotalDistanceTravelledBetweenTimeAndImei(startTime, intervalEndTime, imei)))));
            startTime = intervalEndTime;
        }
        log.info("timeFilter {} size of timeIntervals {}", timeFilter, timeIntervals.size());
        return timeIntervals;
    }

    @Override
    public VehicleFieldDto getVehicleByFilter(SearchFilterDto searchFilter, Long organisationId, Pageable pageable) {
        CustomOrganisation customOrganisation = (CustomOrganisation) customOrganisationRepository.findById(organisationId).orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("ORG_NOT_FOUND")));
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Vehicle> query = criteriaBuilder.createQuery(Vehicle.class);
        Root<Vehicle> root = query.from(Vehicle.class);
        Join<Vehicle, VehicleModel> vehicleModelJoin = root.join("vehicleModel");
        Join<Vehicle, VehicleModel> colorModelJoin = root.join("colorModel");
        List<Predicate> searchPredicateList = getPredicates(searchFilter, customOrganisation, criteriaBuilder, root, vehicleModelJoin);
        query.where(searchPredicateList.toArray(new Predicate[0]));
        Long totalRecords = getTotalRecords(searchFilter, customOrganisation);
        log.debug("{}", totalRecords);

        List<Order> orders = new ArrayList<>();
        pageable.getSort().forEach(sort -> {
            if (sort.getProperty().equals("modelName")) {
                log.debug("sort field {}", sort.getProperty());
                orders.add(sort.isAscending() ? criteriaBuilder.asc(vehicleModelJoin.get("name")) : criteriaBuilder.desc(vehicleModelJoin.get("name")));
            } else if (sort.getProperty().equals("color")) {
                log.debug("sort field {}", sort.getProperty());
                orders.add(sort.isAscending() ? criteriaBuilder.asc(colorModelJoin.get("name")) : criteriaBuilder.desc(colorModelJoin.get("name")));
            } else if (sort.getProperty().equals("netWeight")) {
                log.debug("sort field {}", sort.getProperty());
                orders.add(sort.isAscending() ? criteriaBuilder.asc(vehicleModelJoin.get("netWeight")) : criteriaBuilder.desc(vehicleModelJoin.get("netWeight")));
            } else {
                log.debug("sort field {}", sort.getProperty());
                orders.add(sort.isAscending() ? criteriaBuilder.asc(root.get(sort.getProperty())) : criteriaBuilder.desc(root.get(sort.getProperty())));
            }
        });
        query.orderBy(orders);
        TypedQuery<Vehicle> typedQuery = entityManager.createQuery(query);
        typedQuery.setFirstResult((int) pageable.getOffset());
        log.debug("{}", (int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());
        log.debug("{}", pageable.getPageSize());
        List<Vehicle> vehicleList = typedQuery.getResultList();
        List<VehicleDetailDto> vehicleDetailDtoList = vehicleList.stream()
                .map(vehicle -> {
                    VehicleIdsProjection ids = populateVehicleIdentifiers(vehicle.getImei()).get();
                    VehicleDetailDto vehicleDetailDto = new VehicleDetailDto(ids.getVehChassisNo(), ids.getVehRegNo(), ids.getVehId(), ids.getVehImei());
                    vehicleDetailDto.setImei(vehicle.getImei());
                    vehicleDetailDto.setModelName(vehicle.getVehicleModel().getName());
                    vehicleDetailDto.setChassisNumber(vehicle.getChassisNumber());
                    vehicleDetailDto.setOperationStatus(vehicle.getOperationStatus());
                    vehicleDetailDto.setNetWeight(vehicle.getVehicleModel().getNetWeight());
                    vehicleDetailDto.setColor(vehicle.getColorModel().getName());
                    vehicleDetailDto.setMfrDate(vehicle.getMfrDate());
                    vehicleDetailDto.setVehicleModelId(vehicle.getVehicleModel().getId());
                    return vehicleDetailDto;
                })
                .collect(Collectors.toList());
        Page<VehicleDetailDto> page = new PageImpl<>(vehicleDetailDtoList, pageable, totalRecords);
        return new VehicleFieldDto(page.getContent(), page.getTotalPages(), page.getTotalElements());
    }

    @Override
    public SearchFilterResultDto getAlert(SearchFilterRequestDto searchFilterRequestDto, Long organisationId, Pageable pageable) {
        CustomOrganisation customOrganisation = (CustomOrganisation) customOrganisationRepository.findById(organisationId).orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("ORG_NOT_FOUND")));
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<RangeAlert> query = criteriaBuilder.createQuery(RangeAlert.class);
        Root<RangeAlert> root = query.from(RangeAlert.class);
        List<Predicate> searchPredicateList = getSearchPredicates(searchFilterRequestDto, customOrganisation, criteriaBuilder, root);
        query.where(searchPredicateList.toArray(new Predicate[0]));
        query.orderBy(criteriaBuilder.desc(root.get("timeBucketMinute")));
        Long totalRecords = getTotalAlertRecords(searchFilterRequestDto, customOrganisation);
        log.debug("total records {}", totalRecords);
        TypedQuery<RangeAlert> typedQuery = entityManager.createQuery(query);
        typedQuery.setFirstResult((int) pageable.getOffset());
        log.debug("page Offset {}", (int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());
        log.debug("page size {}", pageable.getPageSize());
        List<RangeAlert> rangeAlertList = typedQuery.getResultList();
        List<VehicleAlertDto> vehicleAlertDtoListList = rangeAlertList.stream()
                .map(rangeAlert -> {
                    VehicleAlertDto vehicleAlertDto = new VehicleAlertDto();
                    vehicleAlertDto.setImei(rangeAlert.getImei());
                    vehicleAlertDto.setPartName(rangeAlert.getPartType().name());
                    vehicleAlertDto.setCategory(rangeAlert.getCategory().substring(rangeAlert.getCategory().lastIndexOf("_") + 1));
                    log.debug("category {}", rangeAlert.getCategory().substring(rangeAlert.getCategory().lastIndexOf("_") + 1));
                    String formattedCategory = rangeAlert.getCategory().substring(0, rangeAlert.getCategory().indexOf("_"));
                    log.debug("formatted category {}", formattedCategory);
                    vehicleAlertDto.setThreshold(formattedCategory + " " + rangeAlert.getThresholdValue());
                    vehicleAlertDto.setCurrentValue(rangeAlert.getCurrentValue());
                    vehicleAlertDto.setMetric(rangeAlert.getMetric().name());
                    vehicleAlertDto.setTimestamp(rangeAlert.getTimeBucketMinute().toEpochMilli());
                    vehicleAlertDto.setId(rangeAlert.getId());
                    log.debug("timeBucketMinute {}", rangeAlert.getTimeBucketMinute().toEpochMilli());
                    return vehicleAlertDto;
                })
                .collect(Collectors.toList());
        Page<VehicleAlertDto> page = new PageImpl<>(vehicleAlertDtoListList, pageable, totalRecords);
        List<String> imeiList = getImeiList(searchFilterRequestDto, customOrganisation);
        log.debug("size of the list {}", imeiList.size());
        Long outlier = rangeAlertRepository.getTotalCountBetweenTimeStamp(Instant.ofEpochMilli(searchFilterRequestDto.getStartTime()), Instant.ofEpochMilli(searchFilterRequestDto.getEndTime()), imeiList);
        Long criticalAlertCount = rangeAlertRepository.getCriticalAlertCountBetweenTimestamp(Instant.ofEpochMilli(searchFilterRequestDto.getStartTime()), Instant.ofEpochMilli(searchFilterRequestDto.getEndTime()), imeiList);
        Long warningAlertCount = rangeAlertRepository.getWarningAlertCountBetweenTimestamp(Instant.ofEpochMilli(searchFilterRequestDto.getStartTime()), Instant.ofEpochMilli(searchFilterRequestDto.getEndTime()), imeiList);
        Long informationalAlertCount = rangeAlertRepository.getInformationalAlertCountBetweenTimestamp(Instant.ofEpochMilli(searchFilterRequestDto.getStartTime()), Instant.ofEpochMilli(searchFilterRequestDto.getEndTime()), imeiList);
        return new SearchFilterResultDto(outlier, criticalAlertCount, warningAlertCount, informationalAlertCount, page.getContent(), page.getTotalPages(), page.getTotalElements());
    }




    @Override
    public VehicleRunningStats getVehicleRunningStats(CustomOrganisation organisation, Long startTime, Long endTime, Pageable pageable) {
        DecimalFormat df = new DecimalFormat("#.##");
        VehicleRunningStats vehicleRunningStats = new VehicleRunningStats();
        List<String> orgImei = vehicleRepository.findImeiByManufacturerOrDealership(organisation.getId(), organisation.getId());
        Instant start = Instant.ofEpochMilli(startTime);
        Instant end = Instant.ofEpochMilli(endTime);
        Pageable sortedByDesc =
                PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), Sort.by("distance").descending());
        List<VehicleDistanceDataDto> topTenRunningVehicles = vehicleRunningMetricsRepository.getTopRunningVehicleByAscOrDescBetweenTimeAndVehicleIn(start, end, orgImei, sortedByDesc).stream().map(e -> new VehicleDistanceDataDto(e.getImei(), Float.valueOf(df.format(e.getDistance())))).toList();
        Pageable sortedByAsc =
                PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), Sort.by("distance").ascending());
        List<VehicleDistanceDataDto> leastTenRunningVehicles = vehicleRunningMetricsRepository.getTopRunningVehicleByAscOrDescBetweenTimeAndVehicleIn(start, end, orgImei, sortedByAsc).stream().map(e -> new VehicleDistanceDataDto(e.getImei(), Float.valueOf(df.format(e.getDistance())))).toList();
        vehicleRunningStats.setTopRunning(topTenRunningVehicles);
        vehicleRunningStats.setLeastRunning(leastTenRunningVehicles);
        return vehicleRunningStats;
    }

    @Override
    public List<PartReplacementDto> getPartsAssociated(Long vehicleId) {
        if (!vehicleRepository.existsById(vehicleId)) {
            throw new VehicleException(evMessageBundle.getMessage("VEHICLE_NOT_FOUND"));
        }
        return partReplacementLogRepository.findByVehicleId(vehicleId).stream()
                .map(log -> new PartReplacementDto(
                        log.getPartType().name(),
                        log.getPart().getId(),
                        log.getStartTime().toEpochMilli(),
                        log.getEndTime() != null ? log.getEndTime().toEpochMilli() : null
                )).collect(Collectors.toList());
    }

    @Autowired
    ChargingEventDetailsRepository chargingEventDetailsRepository;

    @Override
    public AnalyticsDto getVehicleAnalytics(String vehicleId) {

        Vehicle vehicle = getVehicleByAnyId(vehicleId);
        String imei = vehicle.getImei();
        AnalyticsDto analyticsDto = new AnalyticsDto();

        // total trip counts
        analyticsDto.setTripCount(
                tripRepository.countByVehicleAndSummaryPopulationStatusAndTripTypeIn(vehicle, TestRideSummaryPopulationStatus.COMPLETED, List.of(TripType.AUTOMATIC, TripType.TEST_RIDE))
        );

        // total distance covered during the trip with round off up to 2 decimal
        analyticsDto.setDistanceTravelled(Math.round(Optional.ofNullable(tripRepository.findTotalDistanceByImei(imei,List.of(TripType.AUTOMATIC.name(), TripType.TEST_RIDE.name()), TestRideSummaryPopulationStatus.COMPLETED.name(),
                TripDetailsFields.totalDistance.name())).orElse(0.0) * 100.0) / 100.0
        );

        //max speed during the trip
        analyticsDto.setTopSpeed(
                Optional.ofNullable(vehicleRunningMetricsRepository.findMaxSpeedByImei(imei)).orElse(0.0)
        );

        // info , warning , critical alerts
        VehicleAlertCountProjection counts = rangeAlertRepository.getAlertCountsByImei(imei);
        analyticsDto.setTotalInformationalAlerts(counts.getInfoCount());
        analyticsDto.setTotalCriticalAlerts(counts.getCriticalCount());
        analyticsDto.setTotalWarningAlerts(counts.getWarningCount());

        // motor and battery alarms
        analyticsDto.setTotalBatteryAlarms(Optional.ofNullable(batteryAlarmRepository.countByIdImeiAndIdPartType(imei, PartType.BATTERY.name())).orElse(0L));
        analyticsDto.setTotalMotorAlarms(Optional.ofNullable(batteryAlarmRepository.countByIdImeiAndIdPartType(imei, PartType.MOTOR.name())).orElse(0L));

        // charging stuff
        BatteryDischargeCycleProjection batteryChargeCycleProjection = telemetryBatteryRepository.findLatestChgAndDsgCycleCountForImei(imei);
        Optional.ofNullable(batteryChargeCycleProjection)
                .map(BatteryDischargeCycleProjection::getDsgCycleCount)
                .ifPresent(analyticsDto::setTotalDischargeCycles);
        analyticsDto.setRange(null);
        String avgTimeToFullCharge = chargingEventDetailsRepository.findAvgChargingTimeForVehicleWithinStartSocLessThanAndEndSocGreaterThan(vehicle.getId(),30,100);
        if (avgTimeToFullCharge != null && avgTimeToFullCharge.length() > 0) {
            analyticsDto.setAvgTimeToFullCharge(avgTimeToFullCharge);
        } else {
            analyticsDto.setAvgTimeToFullCharge("N/A");
        }

        return analyticsDto;
    }

    public Vehicle getVehicle(Optional<String> imei, Optional<Long> vehicleId, Optional<String> registrationNumber, Optional<String> chassisId, Long organisationId) {
        StringBuilder sqlQuery = new StringBuilder("SELECT v.* FROM vehicle v LEFT JOIN vehicle_registration_details vr ON v.id = vr.vehicle_id WHERE v.mfr_org_id = :organisationId");

        if (imei.isPresent()) {
            sqlQuery.append(" AND v.imei = :imei");
        } else if (vehicleId.isPresent()) {
            sqlQuery.append(" AND v.id = :vehicleId");
        } else if (registrationNumber.isPresent()) {
            sqlQuery.append(" AND vr.registration_number = :registrationNumber");
        } else if (chassisId.isPresent()) {
            sqlQuery.append(" AND v.chassis_number = :chassisId");
        }
        else{
            throw new IllegalArgumentException(evMessageBundle.getMessage("VEHICLE_IDENFITIER_PATTERN_EMPTY"));
        }
        Query query = entityManager.createNativeQuery(sqlQuery.toString(), Vehicle.class);
        query.setParameter("organisationId", organisationId);
        imei.ifPresent(value -> query.setParameter("imei", value));
        if (imei.isEmpty()) {
            log.debug("inside imei empty");
            vehicleId.ifPresent(value -> query.setParameter("vehicleId", value));
        }
        if (imei.isEmpty() && vehicleId.isEmpty()) {
            log.debug("inside imei empty and vehicleId empty");
            registrationNumber.ifPresent(value -> query.setParameter("registrationNumber", value));
        }
        if (imei.isEmpty() && vehicleId.isEmpty() && registrationNumber.isEmpty()) {
            log.debug("inside imei , vehicleId , regNo empty");
            chassisId.ifPresent(value -> query.setParameter("chassisId", value));
        }
        try {
            return (Vehicle) query.getSingleResult();
        } catch (NoResultException e) {
            throw new VehicleNotFoundException(evMessageBundle.getMessage("VEHICLE_NOT_FOUND"));
        }
    }

    /**
     * Implementation method which checks if the vehicle exists in the database using one of the unique identifier
     * like <li>chassis_no</li><li>reg_no</li><li>imei</li><li>vehicle_id</li>
     *
     * @param identifier
     * @return
     */
    @Override
    public boolean vehicleExists(VehicleIdInfo identifier) {
        return switch (identifier.getIdentifierTypes()) {
            case IMEI -> vehicleRepository.existsByImei(identifier.getIdentifier());
            case CHASSIS_NUMBER -> vehicleRepository.existsByChassisNumber(identifier.getIdentifier());
            case VEHICLE_REGISTRATION_NUMBER ->
                    vehicleRegistrationDetailsRepository.existsByRegistrationNumber(identifier.getIdentifier());
            case VEHICLE_ID -> vehicleRepository.existsById(Long.parseLong(identifier.getIdentifier()));
            default -> vehicleRepository.existsByVehicleIdentifier(identifier.getIdentifier());
        };
    }

    /**
     * Implementation to get the {@link Optional<Vehicle>} from the database using one of the unique identifier
     * like <li>chassis_no</li><li>reg_no</li><li>imei</li><li>vehicle_id</li>
     *
     * @param identifier
     * @return
     */
    @Override
    public Optional<Vehicle> getVehicle(String identifier) {


        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();

        if (requestAttributes != null) {
            Object identifierType = requestAttributes.getAttribute("identifierType", RequestAttributes.SCOPE_REQUEST);
            if (identifierType != null) {
                VehicleIdentifierTypes identifierTypes = VehicleIdentifierTypes.fromLabel((String) identifierType);
                return switch (identifierTypes) {
                    case VEHICLE_ID -> vehicleRepository.findById(Long.parseLong(identifier));
                    case VEHICLE_REGISTRATION_NUMBER ->
                            vehicleRegistrationDetailsRepository.findByRegistrationNumber(identifier);
                    case CHASSIS_NUMBER -> vehicleRepository.findByChassisNumber(identifier);
                    case IMEI -> vehicleRepository.findByImei(identifier);
                    default -> vehicleRepository.findVehicleByIdentifier(identifier);
                };
            }
        }
        return vehicleRepository.findVehicleByIdentifier(identifier);
    }

    /**
     * Implementation to get the imei no from the vehicle identifiers like
     * <li>imei</li><li>chassis_no</li><li>reg_no</li><li>vehicle_id</li> available from {@link VehicleIdentifierTypes}
     *
     * @param imei
     * @param chassisNo
     * @param regNo
     * @param vehicleId
     * @param request
     * @return imei no
     */
    @Override
    public String getImeiNoFromVehicleIdentifiers(String imei, String chassisNo, String regNo, Long vehicleId, HttpServletRequest request) {
        String identifier = "";
        VehicleIdentifierTypes identifierType = VehicleIdentifierTypes.fromLabel((String) request.getAttribute("identifierType"));


        identifier = switch (identifierType) {
            case IMEI -> imei;
            case CHASSIS_NUMBER -> chassisNo;
            case VEHICLE_REGISTRATION_NUMBER -> regNo;
            case VEHICLE_ID -> vehicleId.toString();
            default -> (String) request.getAttribute("identifier");
        };
        return this.getVehicle(identifier)
                .filter(e -> e.getImei() != null)
                .map(Vehicle::getImei)
                .orElseThrow(() -> new VehicleNotFoundException(evMessageBundle.getMessage("VEHICLE_NOT_FOUND")));
    }

    private Long getTotalAlertRecords(SearchFilterRequestDto searchFilterRequestDto, CustomOrganisation customOrganisation) {
        CriteriaBuilder countBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> countQuery = countBuilder.createQuery(Long.class);
        Root<RangeAlert> countRoot = countQuery.from(RangeAlert.class);
        countQuery.select(countBuilder.count(countRoot));
        List<Predicate> countPredicateList = getSearchPredicates(searchFilterRequestDto, customOrganisation, countBuilder, countRoot);
        countQuery.where(countPredicateList.toArray(new Predicate[0]));
        return entityManager.createQuery(countQuery).getSingleResult();
    }

    private List<Predicate> getSearchPredicates(SearchFilterRequestDto searchFilterRequestDto, CustomOrganisation customOrganisation, CriteriaBuilder criteriaBuilder, Root<RangeAlert> root) {
        List<Predicate> predicateList = new ArrayList<>();
        log.debug("{}", searchFilterRequestDto.getStartTime());
        log.debug("{}", searchFilterRequestDto.getEndTime());
        List<String> imeiList = getImeiList(searchFilterRequestDto, customOrganisation);
        predicateList.add(root.get("imei").in(imeiList));
        if (searchFilterRequestDto.getImei() != null && !searchFilterRequestDto.getImei().isEmpty()) {
            log.debug("imei search filter {}", searchFilterRequestDto.getImei());
            predicateList.add(criteriaBuilder.like(root.get("imei"), "%" + searchFilterRequestDto.getImei() + "%"));
        }
        if (searchFilterRequestDto.getMetric() != null && !searchFilterRequestDto.getMetric().isEmpty()) {
            log.debug("metric search filter {}", searchFilterRequestDto.getMetric());
            predicateList.add(criteriaBuilder.like(root.get("metric").as(String.class), "%" + searchFilterRequestDto.getMetric().toUpperCase() + "%"));
        }
        if (searchFilterRequestDto.getPartName() != null && !searchFilterRequestDto.getPartName().isEmpty()) {
            log.debug("partType search filter {}", searchFilterRequestDto.getPartName());
            predicateList.add(criteriaBuilder.like(root.get("partType").as(String.class), "%" + searchFilterRequestDto.getPartName().toUpperCase() + "%"));
        }
        if (searchFilterRequestDto.getCategory() != null && !searchFilterRequestDto.getCategory().isEmpty()) {
            log.debug("category {}", searchFilterRequestDto.getCategory());
            predicateList.add(criteriaBuilder.like(root.get("category"), "%" + searchFilterRequestDto.getCategory() + "%"));
        }
        predicateList.add(criteriaBuilder.between(root.get("timeBucketMinute"), Instant.ofEpochMilli(searchFilterRequestDto.getStartTime()), Instant.ofEpochMilli(searchFilterRequestDto.getEndTime())));
        return predicateList;
    }



    private List<String> getImeiList(SearchFilterRequestDto searchFilterRequestDto, CustomOrganisation customOrganisation) {
        List<String> imeiList = new ArrayList<>();
        if (searchFilterRequestDto.getImeiList() != null && !searchFilterRequestDto.getImeiList().isEmpty()) {
            log.info("inside if");
            imeiList = rangeAlertRepository.getImeiFromImeiListBetweenTimestamp(Instant.ofEpochMilli(searchFilterRequestDto.getStartTime()), Instant.ofEpochMilli(searchFilterRequestDto.getEndTime()), searchFilterRequestDto.getImeiList());
        } else if (searchFilterRequestDto.getFleetId() != null) {
            log.info("inside else if");
            imeiList = rangeAlertRepository.getImeiListFromFleet(searchFilterRequestDto.getFleetId(), Instant.ofEpochMilli(searchFilterRequestDto.getStartTime()), Instant.ofEpochMilli(searchFilterRequestDto.getEndTime()));
        } else {
            log.info("inside else");
            imeiList = rangeAlertRepository.getImeiListFromOrganisation(customOrganisation.getId(), Instant.ofEpochMilli(searchFilterRequestDto.getStartTime()), Instant.ofEpochMilli(searchFilterRequestDto.getEndTime()));
        }
        log.debug("size of list {}", imeiList.size());
        return imeiList;
    }

    private Long getTotalRecords(SearchFilterDto searchFilter, CustomOrganisation customOrganisation) {
        CriteriaBuilder countBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> countQuery = countBuilder.createQuery(Long.class);
        Root<Vehicle> countRoot = countQuery.from(Vehicle.class);
        countQuery.select(countBuilder.count(countRoot));
        Join<Vehicle, VehicleModel> vehicleModelJoin = countRoot.join("vehicleModel");
        List<Predicate> countPredicateList = getPredicates(searchFilter, customOrganisation, countBuilder, countRoot, vehicleModelJoin);
        countQuery.where(countPredicateList.toArray(new Predicate[0]));
        return entityManager.createQuery(countQuery).getSingleResult();
    }

    private List<Predicate> getPredicates(SearchFilterDto searchFilter, CustomOrganisation customOrganisation, CriteriaBuilder criteriaBuilder, Root<Vehicle> root, Join<Vehicle, VehicleModel> vehicleModelJoin) {
        List<Predicate> predicateList = new ArrayList<>();
        log.debug("org_id {}", customOrganisation.getId());
        predicateList.add(criteriaBuilder.equal(root.get("manufacturer").get("id"), customOrganisation.getId()));
        if (searchFilter.getImei() != null && !searchFilter.getImei().isEmpty()) {
            log.debug("{}", searchFilter.getImei());
            predicateList.add(criteriaBuilder.like(root.get("imei"), "%" + searchFilter.getImei() + "%"));
        }
        if (searchFilter.getModelName() != null && !searchFilter.getModelName().isEmpty()) {
            log.debug("{}", searchFilter.getModelName());
            predicateList.add(criteriaBuilder.like(vehicleModelJoin.get("name"), "%" + searchFilter.getModelName() + "%"));
        }
        if (searchFilter.getChassisNumber() != null && !searchFilter.getChassisNumber().isEmpty()) {
            log.debug("{}", searchFilter.getChassisNumber());
            predicateList.add(criteriaBuilder.like(root.get("chassisNumber"), "%" + searchFilter.getChassisNumber() + "%"));
        }
        if (searchFilter.getOperationStatus() != null) {
            log.debug("{}", searchFilter.getOperationStatus().name());
            predicateList.add(criteriaBuilder.equal(root.get("operationStatus"), searchFilter.getOperationStatus()));
        }
        return predicateList;
    }

    @Override
    public GraphDataResponse getVehicleGraphData(String identifier, Metric metric, Long startTime, Long endTime, String interval) {
        Vehicle vehicle = getVehicleByAnyId(identifier);
        Long orgId = vehicle.getManufacturer().getId();
        String imei = vehicle.getImei();
        log.debug("start time {}", startTime);
        log.debug("end time {}", endTime);
        log.debug("imei {}", imei);
        String metricFinal = metric.name().toLowerCase();
        log.debug("metric {}", metricFinal);
        List<TimeSeriesDto> timeSeriesData;
        List<BoxPlotDto> boxplotData;
        String tableName = getTableName(metric);
        timeSeriesData = getTimeSeriesData(orgId, imei, startTime, endTime, metricFinal, interval, tableName);
        boxplotData = getBoxPlotData(orgId, imei, startTime, endTime, metricFinal, tableName);
        GraphDataResponse graphDataResponse = new GraphDataResponse();
        graphDataResponse.setTimeSeries(timeSeriesData);
        graphDataResponse.setBoxPlotData(boxplotData);
        return graphDataResponse;
    }

    private List<BoxPlotDto> getBoxPlotData(Long orgId, String imei, Long startTime, Long endTime, String metricFinal, String tableName) {
        List<BoxPlotDto> boxPlotData = new ArrayList<>();
        String boxPlotQueryString = "SELECT round(CAST(MIN(" + metricFinal + ") AS numeric), 2) AS minval, "
                + "round(CAST(approx_percentile(0.25, percentile_agg(" + metricFinal + ")) AS numeric), 2) AS q1, "
                + "round(CAST(approx_percentile(0.5, percentile_agg(" + metricFinal + ")) AS numeric), 2) AS median, "
                + "round(CAST(approx_percentile(0.75, percentile_agg(" + metricFinal + ")) AS numeric), 2) AS q3, "
                + "round(CAST(MAX(" + metricFinal + ") AS numeric), 2) AS maxval "
                + "FROM " + tableName + " "
                + "WHERE mfr_org_id = :mfrOrgId "
                + "AND imei = :imei "
                + "AND timestamp BETWEEN :startTime AND :endTime "
                + "AND " + metricFinal + " IS NOT NULL";

        Query boxPlotQuery = entityManager.createNativeQuery(boxPlotQueryString);
        boxPlotQuery.setParameter("imei", imei);
        boxPlotQuery.setParameter("startTime", Instant.ofEpochMilli(startTime));
        boxPlotQuery.setParameter("endTime", Instant.ofEpochMilli(endTime));
        boxPlotQuery.setParameter("mfrOrgId", orgId);
        List<Object[]> resultList = boxPlotQuery.getResultList();
        for (Object[] result : resultList) {
            Float minValue = ((BigDecimal) result[0]).floatValue();
            Float q1 = ((BigDecimal) result[1]).floatValue();
            Float median = ((BigDecimal) result[2]).floatValue();
            Float q3 = ((BigDecimal) result[3]).floatValue();
            Float maxValue = ((BigDecimal) result[4]).floatValue();
            boxPlotData.add(new BoxPlotDto(minValue, q1, median, q3, maxValue));
        }
        return boxPlotData;
    }

    private List<TimeSeriesDto> getTimeSeriesData(Long orgId, String imei, Long startTime, Long endTime, String metricFinal, String interval, String tableName) {
        String timeSeriesQueryString = "SELECT imei,time_bucket(CAST(:interval AS interval), timestamp) AS timebucket,"
                + "AVG(" + metricFinal + ") AS avg_metric FROM " + tableName + " "
                + "WHERE mfr_org_id = :orgId AND imei = :imei AND timestamp BETWEEN :startTime AND :endTime "
                + "AND " + metricFinal + " IS NOT NULL GROUP BY timebucket, imei";

        Query timeSeriesQuery = entityManager.createNativeQuery(timeSeriesQueryString);
        timeSeriesQuery.setParameter("imei", imei);
        timeSeriesQuery.setParameter("interval", interval);
        timeSeriesQuery.setParameter("startTime", Instant.ofEpochMilli(startTime));
        timeSeriesQuery.setParameter("endTime", Instant.ofEpochMilli(endTime));
        timeSeriesQuery.setParameter("orgId", orgId);
        List<TimeSeriesDto> timeSeriesData = new ArrayList<>();
        List<Object[]> resultList = timeSeriesQuery.getResultList();
        for (Object[] result : resultList) {
            String resultImei = (String) result[0];
            Instant resultTimebucket = (Instant) result[1];
            Double resultAvgMetric = (Double) result[2];
            Long timestamp = resultTimebucket.toEpochMilli();
            TimeSeriesDto dto = new TimeSeriesDto(resultImei, timestamp, resultAvgMetric);
            timeSeriesData.add(dto);
        }
        return timeSeriesData;
    }

    private String getTableName(Metric metric) {
        return (metric.equals(Metric.TEMPERATURE_MAX) || metric.equals(Metric.TEMPERATURE_MIN) || metric.equals(Metric.CURRENT) || metric.equals(Metric.BATTERY_VOLT))
                ? "vehicle_battery_data"
                : "vehicle_motor_data";
    }

}
