package com.nichesolv.evahanam.vehicle.service;

import com.nichesolv.evahanam.common.events.VehicleEvent;
import com.nichesolv.evahanam.common.events.VehicleStateEvent;
import com.nichesolv.evahanam.common.jpa.DataFrequencyPlanDetails;
import com.nichesolv.evahanam.vehicle.dto.*;
import com.nichesolv.evahanam.vehicle.dto.metadata.VehicleMinMaxMotorData;
import com.nichesolv.evahanam.vehicle.enums.TimeFilter;
import com.nichesolv.evahanam.vehicle.enums.VehicleState;
import com.nichesolv.evahanam.vehicle.enums.VehicleIdentifierTypes;
import com.nichesolv.evahanam.vehicle.exception.VehicleException;
import com.nichesolv.evahanam.vehicle.jpa.*;
import com.nichesolv.evahanam.vehicleModel.dto.PartModelTreeDto;
import com.nichesolv.evahanam.vehicleModel.enums.PartType;
import com.nichesolv.evahanam.vehicleModel.exception.VehicleModelException;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.usermgmt.user.exception.UserNotFoundException;
import com.nichesolv.usermgmt.user.model.user.BaseUser;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.data.domain.Pageable;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

public interface IVehicleService {

    Long createVehicle(ErpVehicleDto newVehicleDto , BaseUser user);

    List<String> listVehicles(CustomOrganisation customOrganisation, Pageable pageable) throws VehicleException, VehicleModelException;

    /**
     * Return a paginated List of a vehicle's several identifying parameters like <li>registration_number</li><li>imei</li><li>vehicle_id</li><li>chassis_number</li>
     * @param customOrganisation
     * @param pageable
     * @return List<VehicleIdsProjection>
     * @throws VehicleException
     * @throws VehicleModelException
     */
    List<VehicleIdsProjection> listVehiclesByIdentifier(CustomOrganisation customOrganisation,Pageable pageable,VehicleIdentifierTypes idType,String pattern) throws VehicleException, VehicleModelException;

    List<String> listVehiclesWithOutPagination(CustomOrganisation customOrganisation);

    void updateVehicleStatus(OperationStatusDto operationStatusDto);

    void addPartToVehicle(AddPartRequestDto addPartRequestDto , BaseUser user);

    void updateVehicleRegistrationDetails(VehicleRegistrationDto vehicleRegistrationDto);

    Optional<VehicleDto> find(Optional<String> imei);

    void updateVehicleDealership(String imei, String dealershipEmail);

    void sellVehicle(SellVehicleRequestBody sellVehicleRequestBody, HttpServletRequest request) throws UserNotFoundException;

    boolean existsByImei(String imei);

    Float getLatestOdometerReadingThroughMotorSpeed(String imei);

    Float getLatestOdometerReadingThroughLatAndLong(String imei);

    void updateVehicleRunningMetricsAndOdometerInVehicleLatestData(String imei, Instant vehicleStatusAt, DataFrequencyPlanDetails dataFrequencyPlanDetails);

    ExecutiveMetaDataResponseDto getOrganisationMetaDataByOrganisation(CustomOrganisation organisation);

    public ExecutiveMetaDataResponseDto getOrganisationMetaDataByOrganisationBetweenTimestamp(CustomOrganisation organisation, Optional<Long> startTime, Optional<Long> endTime);

    MotorDataResponseDto getData(Long from, Long to, CustomOrganisation organisation);

    VehicleSparklingDataDto getSparklingData(Long from, Long to, String interval, Long id);

    List<VehicleMinMaxMotorData> getVehicleData(VehicleDataDto vehicleDataDto, CustomOrganisation organisation);

    String getVehicleBySerialNumber(String serialNumber);

    public void updateVehicleStatus(Long orgId, Boolean status);

    List<DriveModeAndRangeDto> getDriveModesAndRangesByImei(String identifier);


    VehicleDistanceDto getVehicleDistanceBetween(String imei, Long from, Long to, TimeFilter timeFilter);


    VehiclePartAttributeDetailsDto getPartAttributesByImei(String imei, PartType partType, String attributeName);

    VehicleLatestData saveVehicleLatitudeAndLongitude(String imei, VehicleLatestData vehicleLatestData);

    VehicleLatestData getRecentVehicleBatteryData(String imei, VehicleLatestData vehicleLatestData);

    VehicleEvent saveVehicleState(String imei, Instant vehicleStatusAt, DataFrequencyPlanDetails dataFrequencyPlanDetails);

    void updateVehicleStateForDataDelay(String imei,Instant currentTime,DataFrequencyPlanDetails dataFrequencyPlanDetails);


    VehicleStatusListDto getVehicleByStatus(Optional<Long> fleetId, Optional<String> status, Long organisationId, Pageable pageable);

    VehicleStatusCountDto getVehicleCount(Optional<Long> fleetId, Long organisationId);

    String getVehicleState(String imei, Long organisationId);


    PartModelTreeDto getPartModelTreeView(String imei);


    VehicleFieldDto getVehicleByFilter(SearchFilterDto searchFilterDto, Long organisationId, Pageable pageable);

    VehicleStateEvent updateVehicleRunningEvent(Vehicle vehicle, VehicleStatus vehicleStatus);

    GraphDataResponse getVehicleGraphData(String imei, Metric metric, Long startTime, Long endTime, String interval);



    SearchFilterResultDto getAlert(SearchFilterRequestDto searchFilterRequestDto, Long organisationId, Pageable pageable);



    VehicleStatusListAllDto getVehiclesByVehicleStateWithOutPagination(Optional<Long> fleetId, Optional<VehicleState> status, Long organisationId);

    /**
     * This method checks if a vehicle exists in the database using one of the unique identifier
     * like <li>chassis_no</li><li>reg_no</li><li>imei</li><li>vehicle_id</li>
     * @param identifier
     * @return an Optional<{@link Vehicle}>  object
     */
    boolean vehicleExists(VehicleIdInfo identifier);

    /**
     * This method is used to get the {@link Optional<Vehicle>} from the database using one of the unique identifier
     * like <li>chassis_no</li><li>reg_no</li><li>imei</li><li>vehicle_id</li>
     * @param identifier
     * @return an Optional<{@link Vehicle}>  object
     */
    Optional<Vehicle> getVehicle(String identifier);

    /**
     * This method is used to get the imei no from the vehicle identifiers like
     * <li>imei</li><li>chassis_no</li><li>reg_no</li><li>vehicle_id</li> available from {@link VehicleIdentifierTypes}
     * @param imei
     * @param chassisNo
     * @param regNo
     * @param vehicleId
     * @param request
     * @return imei no or throws an VehicleNotFoundException
     */
    String getImeiNoFromVehicleIdentifiers(String imei, String chassisNo, String regNo,Long vehicleId, HttpServletRequest request);

    /**
     * Populate all identifiers of the vehicle
     * @param identifier
     * @return
     */
    Optional<VehicleIdsProjection> populateVehicleIdentifiers(String identifier);

    Vehicle getVehicleByAnyId(String identifier);

    VehicleRunningStats getVehicleRunningStats(CustomOrganisation organisation, Long startTime, Long endTime, Pageable pageable);

    List<PartReplacementDto> getPartsAssociated(Long vehicleId);

    AnalyticsDto getVehicleAnalytics(String vehicleId);
}