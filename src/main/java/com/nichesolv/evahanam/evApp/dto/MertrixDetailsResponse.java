package com.nichesolv.evahanam.evApp.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MertrixDetailsResponse {
    private List<MertrixDetailsDto> running = new ArrayList<>();
    private List<MertrixDetailsDto> trips = new ArrayList<>();
    private List<MertrixDetailsDto> alerts = new ArrayList<>();
    private List<MertrixDetailsDto> alarms = new ArrayList<>();
}
