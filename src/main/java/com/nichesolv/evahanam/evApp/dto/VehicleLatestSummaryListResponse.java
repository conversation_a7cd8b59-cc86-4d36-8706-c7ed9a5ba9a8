package com.nichesolv.evahanam.evApp.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleLatestSummaryListResponse {
    private  List<VehicleLatestSummaryDto> running = new ArrayList<>();
    private List<VehicleLatestSummaryDto> trips = new ArrayList<>();
    private List<VehicleLatestSummaryDto> alerts = new ArrayList<>();
    private List<VehicleLatestSummaryDto> alarms = new ArrayList<>();
}