package com.nichesolv.evahanam.evApp.controller;

import com.nichesolv.evahanam.common.annotations.ReadOnly;
import com.nichesolv.evahanam.evApp.dto.*;
import com.nichesolv.evahanam.evApp.service.IUserVehicleService;
import com.nichesolv.evahanam.util.HttpRequestOriginUtil;
import com.nichesolv.evahanam.util.UserOrganisationUtils;
import com.nichesolv.nds.dto.organisation.enums.OrganisationType;
import com.nichesolv.nds.exception.UserNotFoundException;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.model.user.CustomUser;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@Slf4j
@RestController
@RequestMapping("/user-vehicles")
@SecurityRequirement(name = "Bearer Authentication")
public class UserVehicleController {

    @Autowired
    IUserVehicleService iUserVehicleService;

    @Autowired
    HttpRequestOriginUtil httpRequestOriginUtil;

    @GetMapping("/connections")
    @ReadOnly
    @SecurityRequirement(name = "Bearer Authentication")
    public List<UserVehicleLastConnectionDetailsDto> findUserPreviousVehicle(@AuthenticationPrincipal CustomUser user,
                                                                             @RequestParam(name = "key", required = false) String key,
                                                                             @Validated @PageableDefault(size = 20) @SortDefault(direction = Sort.Direction.DESC, sort = "startOn") Pageable pageable, @RequestParam Optional<Long> endTime, @RequestParam(value = "organisationType", required = true) OrganisationType organisationType, @RequestParam(value = "orgId", required = true) Long orgId) throws UserNotFoundException {
        return iUserVehicleService.findUserVehiclesLastConnectionDetails(user, (key == null ? "" : key), pageable, organisationType, orgId);
    }

    @GetMapping("/insights")
    @ReadOnly
    @SecurityRequirement(name = "Bearer Authentication")
    public UserVehicleInsightsStaticsDto findUserVehicleInsightsStatistics(@AuthenticationPrincipal CustomUser user, @RequestParam Optional<Long> startTime,
                                                                           @RequestParam Optional<Long> endTime, @RequestParam(value = "organisationType", required = true) OrganisationType organisationType, @RequestParam(value = "orgId", required = true) Long orgId) {
        return iUserVehicleService.findUserVehiclesInsightStatistics(user, startTime, endTime, organisationType, orgId);
    }

    @GetMapping("/insights/details")
    @ReadOnly
    @SecurityRequirement(name = "Bearer Authentication")
    public UserVehicleInsightsStaticsDetailsDto findUserVehicleInsightsStatisticsDetails(@AuthenticationPrincipal CustomUser user, @RequestParam Optional<Long> startTime,
                                                                                         @RequestParam Optional<Long> endTime, @RequestParam String dataType, @RequestParam String period, @RequestParam(value = "organisationType", required = true) OrganisationType organisationType,  @RequestParam(value = "orgId", required = true) Long orgId) {
        return iUserVehicleService.findUserVehiclesInsightStatisticsDetails(user, startTime, endTime, dataType, period, organisationType, orgId);
    }

    @GetMapping("/test-rides/metadata")
    @ReadOnly
    public UserVehicleTestDetailDto getUserVehicleTestDetails(@AuthenticationPrincipal CustomUser user,
                                                              @RequestParam(value = "vIdVal", required = false) String idValue,
                                                              @RequestAttribute(value = "identifier",required = false) String identifier) {
        log.info("inside the controller user vehicle test details");
        log.info("identifier {}, vIdVal {}", identifier, idValue);
        return iUserVehicleService.getUserVehicleTest(user, identifier);
    }

    @GetMapping("/trips")
    @ReadOnly
    public List<UserVehicleTestDetailDto> getTripWithVehicle(@AuthenticationPrincipal CustomUser user,
                                                             @RequestParam(value = "vIdVal", required = false) String idValue,
                                                             @RequestAttribute(value = "identifier", required = false) String identifier,
                                                             @PageableDefault(size = 8) @SortDefault(direction = Sort.Direction.DESC, sort = "startTime") Pageable pageable) {
        log.debug(" inside the controller ");
        return iUserVehicleService.getUserVehicleTripHistory(identifier, user, pageable);
    }

    @GetMapping("/mertrix-details")
    public VehicleLatestSummaryListResponse getLatestSummaryList(

            @RequestParam(value = "startTime", required = true) Long startTime,
            @RequestParam(value = "endTime", required = true) Long endTime,
            @PageableDefault(size = 10, page = 0) Pageable pageable , HttpServletRequest request
            ) {
      CustomOrganisation organisation = httpRequestOriginUtil.getUserOrganisation(request);

        return iUserVehicleService.getLatestSummary (  organisation.getId(), startTime, endTime, pageable);
    }



}
